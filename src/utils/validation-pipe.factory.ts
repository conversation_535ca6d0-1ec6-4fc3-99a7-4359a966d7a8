import { HttpException, ValidationPipe } from "@nestjs/common";
import { APP_PIPE } from "@nestjs/core";
import { ValidationError } from "class-validator";

import { NestedRecord } from "./nested-record";

const getValidationErrors = (errors: ValidationError[]) => {
  return errors.reduce((acc: NestedRecord<string>, error) => {
    const constraints = error.constraints;

    if (error.children?.length) {
      acc[error.property] = getValidationErrors(error.children);
      return acc;
    }

    if (constraints) {
      if (constraints.isUrl) {
        acc[error.property] = "This field must be a valid URL.";
        return acc;
      }

      if (constraints.isEmail) {
        acc[error.property] = "This field must be a valid email address.";
        return acc;
      }

      if (constraints.isNotEmpty) {
        acc[error.property] = "This field is required.";
        return acc;
      }

      if (constraints.isStrongPassword) {
        acc[error.property] =
          "The password must be at least 8 characters long and contain at least one lowercase letter, one uppercase letter, one number, and one special character.";
        return acc;
      }

      // if no match, pass down the original errors
      acc[error.property] = Object.values(constraints).join(", ");
      return acc;
    }

    return acc;
  }, {});
};

export const ValidationPipeFactory = {
  provide: APP_PIPE,
  useFactory: () => {
    return new ValidationPipe({
      transform: true,
      whitelist: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const validationErrors = getValidationErrors(errors);

        return new HttpException({ validationErrors }, 400);
      },
    });
  },
};
