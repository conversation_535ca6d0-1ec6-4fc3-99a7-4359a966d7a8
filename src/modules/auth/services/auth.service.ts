import { Injectable, NotFoundException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";

import { Admin } from "src/modules/admin/admin.entity";
import { AdminService } from "src/modules/admin/admin.service";
import { User } from "src/modules/user/user.entity";
import { UserService } from "src/modules/user/user.service";
import { RequestUser } from "src/utils/decorators/current-user.decorator";
import {
  DUMMY_PASSWORD_HASH,
  validatePassword,
} from "src/utils/password/password";
import { Role } from "src/utils/roles/role";

const LOGIN_ATTEMPT_LIMIT = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

type LoginRejectionReason = "invalid" | "locked" | "not_found";

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private adminService: AdminService,
    private jwtService: JwtService,
  ) {}

  async validateAdmin(email: string, pass: string) {
    return await this.validate(email, pass, this.adminService);
  }

  async validateUser(email: string, pass: string) {
    return await this.validate(email, pass, this.userService);
  }

  private async validate<T extends AdminService | UserService>(
    email: string,
    pass: string,
    service: T,
  ): Promise<{
    entity?: T extends AdminService
      ? Omit<Admin, "password">
      : Omit<User, "password">;
    reason?: LoginRejectionReason;
  }> {
    const entity = await service.findByEmail(email);
    if (!entity) {
      // mitigate timing attack
      await validatePassword(pass, DUMMY_PASSWORD_HASH);
      return { reason: "not_found" };
    }

    const isPastLockoutTime =
      entity.lastFailedLoginAttempt &&
      Date.now() - entity.lastFailedLoginAttempt.getTime() > LOCKOUT_DURATION;
    const isLocked =
      entity.failedLoginAttempts >= LOGIN_ATTEMPT_LIMIT && !isPastLockoutTime;

    if (isLocked) {
      return { reason: "locked" };
    }

    if (await validatePassword(pass, entity.password)) {
      const { password: _password, ...rest } = entity;

      await service.updateLockout(entity.id, 0);

      return {
        entity: rest as T extends AdminService
          ? Omit<Admin, "password">
          : Omit<User, "password">,
      };
    }

    if (isPastLockoutTime) {
      await service.updateLockout(entity.id, 1);
    } else {
      await service.updateLockout(entity.id, entity.failedLoginAttempts + 1);
    }

    return { reason: "invalid" };
  }

  async login<IsAdmin extends true | false>(
    user: Pick<User | Admin, "id">,
    isAdmin: IsAdmin,
  ): Promise<{ jwt: string; user: IsAdmin extends true ? Admin : User }> {
    const jwt = this.jwtService.sign({ sub: user.id, isAdmin });

    if (isAdmin === true) {
      const foundUser = await this.adminService.findOrFail(user.id);
      // ts can't narrow down
      return { jwt, user: foundUser as IsAdmin extends true ? Admin : User };
    }

    const foundUser = await this.userService.findOrFail(user.id);
    return { jwt, user: foundUser as IsAdmin extends true ? Admin : User };
  }

  async revalidateToken(user: Pick<User | Admin, "id">, isAdmin: boolean) {
    return this.jwtService.sign({ sub: user.id, isAdmin });
  }

  async getRequestUser<T extends Role>(
    options:
      | {
          email: string;
          role: Role.Admin;
        }
      | {
          email: string;
          role: Role.Consultant | Role.Client;
          accountId: string;
        },
  ): Promise<RequestUser<T>> {
    if (options.role === Role.Admin) {
      const admin = await this.adminService.findByEmail(options.email);
      if (!admin) {
        throw new NotFoundException("Admin not found.");
      }

      return { user: admin, role: Role.Admin } as RequestUser<T>;
    }

    const user = await this.userService.findByEmail(options.email);
    if (!user) {
      throw new NotFoundException("User not found.");
    }

    if (options.role === Role.Consultant) {
      const consultantAccount = user.consultantAccounts.find(
        (account) => account.id === options.accountId,
      );
      if (!consultantAccount) {
        throw new NotFoundException("Consultant account not found.");
      }

      return {
        user,
        role: Role.Consultant,
        consultantAccount,
      } as RequestUser<T>;
    }

    const clientAccount = user.clientAccounts.find(
      (account) => account.id === options.accountId,
    );
    if (!clientAccount) {
      throw new NotFoundException("Client account not found.");
    }

    return {
      user,
      role: Role.Client,
      clientAccount,
    } as RequestUser<T>;
  }
}
