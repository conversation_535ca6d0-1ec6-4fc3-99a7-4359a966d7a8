import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCompanyScoring1753876635998 implements MigrationInterface {
  name = "AddCompanyScoring1753876635998";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company" ADD "risk_min_score" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD "risk_max_score" integer NOT NULL DEFAULT '5'`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD "risk_step" numeric NOT NULL DEFAULT '0.25'`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD CONSTRAINT "CHK_8cc674ade691f974480e173830" CHECK ((risk_max_score - risk_min_score) % risk_step = 0)`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD CONSTRAINT "CHK_784264db3ee6b65586d6790736" CHECK (risk_max_score - risk_min_score >= 0)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company" DROP CONSTRAINT "CHK_784264db3ee6b65586d6790736"`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" DROP CONSTRAINT "CHK_8cc674ade691f974480e173830"`,
    );
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "risk_step"`);
    await queryRunner.query(
      `ALTER TABLE "company" DROP COLUMN "risk_max_score"`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" DROP COLUMN "risk_min_score"`,
    );
  }
}
