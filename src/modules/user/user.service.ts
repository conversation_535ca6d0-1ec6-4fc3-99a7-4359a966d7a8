import {
  HttpException,
  ConflictException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { QueryDeepPartialEntity } from "typeorm/query-builder/QueryPartialEntity";

import { hashPassword } from "@utils/password/password";

import { Invitation } from "@invitation/invitation.entity";

import { User } from "./user.entity";

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    return await this.userRepository.find({
      relations: { consultantAccounts: true, clientAccounts: true },
    });
  }

  async createFromInvitation(
    invitation: Invitation,
    password: string,
  ): Promise<User> {
    const hashedPassword = await hashPassword(password);

    const { email } = invitation;

    const existingUser = await this.findByEmail(email);
    if (existingUser) {
      throw new ConflictException("User already exists.");
    }

    return await this.userRepository.save(
      this.userRepository.create({
        email,
        password: hashedPassword,
      }),
    );
  }

  async update(
    id: string,
    updateUserDto: QueryDeepPartialEntity<User> & { password?: string },
  ): Promise<User> {
    const { password, ...rest } = updateUserDto;

    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException("User not found.");
    }

    const hashedPassword = password ? await hashPassword(password) : undefined;

    await this.userRepository.update(
      { id },
      {
        ...rest,
        ...(password ? { password: hashedPassword } : {}),
      },
    );

    return await this.userRepository.findOneOrFail({
      where: { id },
      relations: { consultantAccounts: true, clientAccounts: true },
    });
  }

  async updateLockout(id: string, failedLoginAttempts: number): Promise<void> {
    await this.userRepository.update(
      { id },
      {
        failedLoginAttempts,
        lastFailedLoginAttempt: failedLoginAttempts === 0 ? null : new Date(),
      },
    );
  }

  async delete(id: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: {
        consultantAccounts: true,
        clientAccounts: true,
      },
    });
    if (!user) {
      throw new NotFoundException("User not found.");
    }

    await this.userRepository.delete(id);
    return;
  }

  async find(id: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: {
        id,
      },
      relations: {
        consultantAccounts: {
          consultant: true,
        },
        clientAccounts: {
          company: {
            consultant: true,
          },
        },
      },
    });
  }

  async findOrFail<T extends HttpException>(
    id: string,
    Exception?: T,
  ): Promise<User> {
    const user = await this.find(id);

    if (!user) {
      throw Exception ?? new NotFoundException("User not found.");
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: {
        email,
      },
      relations: {
        consultantAccounts: { consultant: true },
        clientAccounts: { company: true },
      },
    });
  }

  async findByResetPasswordToken(token: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: {
        resetPasswordToken: token,
      },
    });
  }

  async findByResetPasswordTokenOrFail(token: string): Promise<User> {
    const user = await this.findByResetPasswordToken(token);

    if (!user) {
      throw new NotFoundException("User not found.");
    }

    return user;
  }
}
