import { PipeTransform, Injectable, BadRequestException } from "@nestjs/common";

@Injectable()
export class ParseNanoIDPipe implements PipeTransform {
  private readonly nanoidRegex = /^[A-Za-z0-9]{21}$/;

  transform(value: string): string {
    if (typeof value !== "string" || !this.nanoidRegex.test(value)) {
      throw new BadRequestException(
        `Validation failed (NanoID must be 21 alphanumeric characters)`,
      );
    }
    return value;
  }
}
