import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
} from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import { Public } from "src/modules/auth/decorators/public.decorator";
import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { ParseNanoIDPipe } from "src/utils/nanoid";

import { BatchCreateInvitationDto } from "./dtos/batch-create-invitation.dto";
import { ValidateInvitationResponseDto } from "./dtos/validate-invitation-response.dto";
import { InvitationService } from "./invitation.service";

@Controller("invitation")
export class InvitationController {
  constructor(private readonly invitationService: InvitationService) {}

  // @Post()
  // async create(
  //   @CurrentUser() currentUser: RequestUser,
  //   @Body() createInvitationDto: CreateInvitationDto,
  // ): Promise<Invitation> {
  //   return await this.invitationService.create(
  //     currentUser,
  //     createInvitationDto,
  //   );
  // }

  @Post("batch")
  async batchCreate(
    @CurrentUser() currentUser: RequestUser,
    @Body() createInvitationDto: BatchCreateInvitationDto,
  ): Promise<void> {
    await this.invitationService.canCreate(currentUser, createInvitationDto);

    return await this.invitationService.batchCreate(
      currentUser,
      createInvitationDto,
    );
  }

  // @Post("renew/:token")
  // @Public()
  // async renew(
  //   @Param("token", ParseNanoIDPipe) token: string,
  // ): Promise<Invitation> {
  //   return await this.invitationService.renew(token);
  // }

  @Get("validate/:token")
  @Public()
  async validate(
    @Param("token", ParseNanoIDPipe) token: string,
  ): Promise<ValidateInvitationResponseDto> {
    const result = await this.invitationService.validateByToken(token);

    return plainToInstance(ValidateInvitationResponseDto, result, {
      excludeExtraneousValues: true,
    });
  }

  @Get("render-email/:token")
  async renderInvitationEmail(
    @Param("token", ParseNanoIDPipe) token: string,
  ): Promise<string> {
    const invitation = await this.invitationService.findByToken(token);

    if (!invitation) {
      throw new NotFoundException(`Invitation with token: ${token} not found.`);
    }

    return await this.invitationService.renderInvitationEmail(invitation);
  }
}
