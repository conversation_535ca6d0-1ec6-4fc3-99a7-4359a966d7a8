import { IsEmail } from "class-validator";
import {
  Column,
  <PERSON>tity,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
  Index,
  OneToOne,
  AfterLoad,
} from "typeorm";

import { Admin } from "src/modules/admin/admin.entity";
import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";
import { IsUrlSafeNanoId } from "src/utils/nanoid";

import { generateInvitationExpiration, generateInvitationToken } from "./util";
import { ClientAccount } from "../client-account/client-account.entity";
import { Company } from "../company/company.entity";
import { Consultant } from "../consultant/consultant.entity";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";

export enum InvitationStatus {
  Pending = "pending",
  Active = "active",
  Expired = "expired",
  Invalid = "invalid",
}

@Entity()
export class Invitation extends BaseEntity {
  @Column()
  @IsUrlSafeNanoId()
  @Index({ unique: true })
  token: string;

  @Column()
  @IsEmail()
  email: string;

  @Column()
  name: string;

  @NullableStringColumn({ name: "job_title" })
  jobTitle: string | null;

  @Column({ type: "timestamp", name: "expires_at" })
  expiresAt: Date;

  @Column({
    type: "jsonb",
    array: false,
    nullable: true,
  })
  meta: Record<string, unknown> | null;

  @ManyToOne(() => Consultant, (consultant) => consultant.invitations, {
    eager: true,
  })
  @JoinColumn({ name: "consultant_id" })
  consultant: Consultant | null;

  @ManyToOne(() => Company, (company) => company.invitations, { eager: true })
  @JoinColumn({ name: "company_id" })
  company: Company | null;

  @ManyToOne(
    () => ConsultantAccount,
    (consultantAccount) => consultantAccount.createdInvitations,
    { onDelete: "SET NULL" },
  )
  @JoinColumn({ name: "created_by_consultant_account_id" })
  createdByConsultantAccount: ConsultantAccount | null;

  @ManyToOne(
    () => ClientAccount,
    (clientAccount) => clientAccount.createdInvitations,
    { onDelete: "SET NULL" },
  )
  @JoinColumn({ name: "created_by_client_account_id" })
  createdByClientAccount: ClientAccount | null;

  @ManyToOne(() => Admin, (admin) => admin.createdInvitations, {
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "created_by_admin_id" })
  createdByAdmin: Admin | null;

  @OneToOne(
    () => ConsultantAccount,
    (consultantAccount) => consultantAccount.invitation,
    { onDelete: "CASCADE", eager: true },
  )
  @JoinColumn({ name: "created_consultant_account_id" })
  createdConsultantAccount: ConsultantAccount | null;

  @OneToOne(() => ClientAccount, (clientAccount) => clientAccount.invitation, {
    onDelete: "CASCADE",
    eager: true,
  })
  @JoinColumn({ name: "created_client_account_id" })
  createdClientAccount: ClientAccount | null;

  status: InvitationStatus;

  @AfterLoad()
  calculateStatus() {
    if (this.createdConsultantAccount || this.createdClientAccount) {
      return (this.status = InvitationStatus.Active);
    }

    if (!this.company && !this.consultant) {
      return (this.status = InvitationStatus.Invalid);
    }

    if (this.expiresAt < new Date()) {
      return (this.status = InvitationStatus.Expired);
    }

    return (this.status = InvitationStatus.Pending);
  }

  @BeforeInsert()
  setDefaultValues() {
    this.token = generateInvitationToken();
    this.expiresAt = generateInvitationExpiration();
  }
}
