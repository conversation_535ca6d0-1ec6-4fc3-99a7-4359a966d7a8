import { <PERSON>, <PERSON>, Param, <PERSON>, <PERSON> } from "@nestjs/common";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { JobType } from "./enum/job-type.enum";
import { Job } from "./job.entity";
import { JobService } from "./job.service";

@Controller("job")
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @Get(":jobId")
  @Roles(Role.Consultant)
  async findById(
    @Param("jobId") jobId: string,
    @CurrentUser() user: RequestUser<Role.Consultant>,
  ): Promise<Job> {
    await this.jobService.canAccess(user, jobId);

    return this.jobService.findOneOrFail(jobId);
  }

  @Post(":jobType/:id")
  @Roles(Role.Consultant)
  async addJob(
    @Param("id") id: string,
    @Param("jobType") jobType: string,
    @CurrentUser() user: RequestUser<Role.Consultant>,
  ): Promise<Job | null> {
    await this.jobService.canCreate(user, {
      type: jobType as JobType,
      id,
    });

    const job = await this.jobService.create({
      type: jobType as JobType,
      id,
    });

    if (!job) {
      return null;
    }

    return job;
  }

  @Patch(":jobId/cancel")
  @Roles(Role.Consultant)
  async cancelJob(
    @Param("jobId") jobId: string,
    @CurrentUser() user: RequestUser<Role.Consultant>,
  ): Promise<Job | null> {
    await this.jobService.canManage(user, jobId);

    return await this.jobService.cancelJob(jobId);
  }
}
