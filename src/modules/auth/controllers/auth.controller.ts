import {
  Controller,
  Get,
  HttpCode,
  Post,
  Request,
  Response,
  UseGuards,
} from "@nestjs/common";
import { Response as ExpressResponse } from "express";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { AdminService } from "../../admin/admin.service";
import { UserService } from "../../user/user.service";
import { DoNotRevalidateToken } from "../decorators/do-not-revalidate-token.decorator";
import { Public } from "../decorators/public.decorator";
import { JwtAuthGuard } from "../guards/jwt-auth.guard";
import { LocalAdminAuthGuard } from "../guards/local-admin-auth.guard";
import { LocalUserAuthGuard } from "../guards/local-user-auth.guard";
import { AuthService } from "../services/auth.service";

export const JWT_TOKEN_DURATION = 8 * 60 * 60;
export const HTTP_COOKIE_NAME = "token";

export const HTTP_COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV !== "development",
  sameSite: "strict",
  maxAge: JWT_TOKEN_DURATION * 1000,
} as const;

@Controller("auth")
@DoNotRevalidateToken()
export class AuthController {
  constructor(
    private authService: AuthService,
    private adminService: AdminService,
    private userService: UserService,
  ) {}

  @Post("user/login")
  @Public()
  @HttpCode(200)
  @UseGuards(LocalUserAuthGuard)
  async loginUser(
    @Request() req: any,
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const { jwt, user } = await this.authService.login(req.user, false);

    res.cookie(HTTP_COOKIE_NAME, jwt, HTTP_COOKIE_CONFIG);

    return user;
  }

  @Post("user/logout")
  @Public()
  @HttpCode(200)
  async logoutUser(@Response({ passthrough: true }) res: ExpressResponse) {
    res.clearCookie(HTTP_COOKIE_NAME);

    return;
  }

  @Post("admin/login")
  @Public()
  @HttpCode(200)
  @UseGuards(LocalAdminAuthGuard)
  async loginAdmin(
    @Request() req: any,
    @Response({ passthrough: true }) res: ExpressResponse,
  ) {
    const { jwt, user } = await this.authService.login(req.user, true);

    res.cookie(HTTP_COOKIE_NAME, jwt, HTTP_COOKIE_CONFIG);

    return user;
  }

  @Post("admin/logout")
  @Public()
  @HttpCode(200)
  async logoutAdmin(@Response({ passthrough: true }) res: ExpressResponse) {
    res.clearCookie(HTTP_COOKIE_NAME);

    return;
  }

  @Get("me")
  @HttpCode(200)
  @UseGuards(JwtAuthGuard)
  async me(@CurrentUser() user: RequestUser) {
    if (user.role === Role.Admin) {
      return await this.adminService.findOrFail(user.user.id);
    }

    return await this.userService.findOrFail(user.user.id);
  }
}
