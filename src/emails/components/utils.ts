export const mcn = (...args: unknown[]) =>
  args
    .filter((arg): arg is string => typeof arg === "string" && arg !== "")
    .join(" ");

function hashString(str: string) {
  let hash = 2166136261;

  for (let i = 0; i < str.length; i++) {
    hash ^= str.charCodeAt(i);
    hash *= 16777619;
  }
  return hash >>> 0;
}

export function initialsFromString(name: string) {
  if (!name || typeof name !== "string") {
    return "";
  }

  const parts = name.trim().split(/\s+/).filter(Boolean);

  if (parts.length === 1) {
    return parts[0]!.substring(0, 2).toUpperCase();
  }

  const firstInitial = parts[0]?.[0] || "";
  const lastInitial = parts[parts.length - 1]?.[0] || "";

  return (firstInitial + lastInitial).toUpperCase();
}

/* Color transformation from lch to rgb is AI generated */
export function colorFromString(str: string): string {
  const hash = hashString(str);
  const h = hash % 360;
  const c = 90;
  const l = 40 + (hash % 20);
  const [r, g, b] = lchToRgb(l, c, h);
  return rgbToHex(r, g, b);
}

function lchToRgb(L: number, C: number, H: number): [number, number, number] {
  const hr = (H * Math.PI) / 180;
  const a = C * Math.cos(hr);
  const bb = C * Math.sin(hr); // renamed to avoid conflict

  const Y = (L + 16) / 116;
  const X = a / 500 + Y;
  const Z = Y - bb / 200;

  const xyz = [X, Y, Z].map((t) => {
    const t3 = t ** 3;
    return t3 > 0.008856 ? t3 : (t - 16 / 116) / 7.787;
  });

  let [x, y, z] = xyz;
  x *= 0.95047;
  z *= 1.08883;

  let r = x * 3.2406 + y * -1.5372 + z * -0.4986;
  let g = x * -0.9689 + y * 1.8758 + z * 0.0415;
  let b = x * 0.0557 + y * -0.204 + z * 1.057;

  const rgb = [r, g, b].map((v) => {
    v = v <= 0.0031308 ? 12.92 * v : 1.055 * v ** (1 / 2.4) - 0.055;
    return Math.round(Math.max(0, Math.min(1, v)) * 255);
  });

  return rgb as [number, number, number];
}

function rgbToHex(r: number, g: number, b: number): string {
  return "#" + [r, g, b].map((x) => x.toString(16).padStart(2, "0")).join("");
}
