import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { ContextReport } from "../context-report/context-report.entity";

@Entity()
export class Section extends BaseEntity {
  @NullableStringColumn()
  title: string | null;

  @Column()
  level: number;

  @Column()
  position: number;

  @NullableStringColumn()
  prompt: string | null;

  @NullableStringColumn()
  response: string | null;

  @NullableStringColumn()
  text: string | null;

  @NullableStringColumn({ name: "cited_from" })
  citedFrom: string | null;

  @ManyToOne(() => Section, (section) => section.children, {
    nullable: true,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "parent_id" })
  parent: Section | null;

  @OneTo<PERSON>any(() => Section, (section) => section.parent, {
    cascade: true,
  })
  children: Section[];

  @ManyToOne(() => ContextReport, (contextReport) => contextReport.sections, {
    nullable: false,
  })
  @JoinColumn({ name: "context_report_id" })
  contextReport: ContextReport;

  @Column({ name: "context_report_id" })
  contextReportId: string;
}
