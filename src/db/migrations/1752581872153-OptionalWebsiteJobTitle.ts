import { MigrationInterface, QueryRunner } from "typeorm";

export class OptionalWebsiteJobTitle1752581872153
  implements MigrationInterface
{
  name = "OptionalWebsiteJobTitle1752581872153";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invitation" ALTER COLUMN "job_title" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" ALTER COLUMN "job_title" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant" ALTER COLUMN "website" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "website" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account" ALTER COLUMN "job_title" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "client_account" ALTER COLUMN "job_title" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "website" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant" ALTER COLUMN "website" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" ALTER COLUMN "job_title" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ALTER COLUMN "job_title" SET NOT NULL`,
    );
  }
}
