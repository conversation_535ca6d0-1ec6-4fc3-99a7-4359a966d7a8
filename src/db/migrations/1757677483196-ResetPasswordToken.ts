import { MigrationInterface, QueryRunner } from "typeorm";

export class ResetPasswordToken1757677483196 implements MigrationInterface {
  name = "ResetPasswordToken1757677483196";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "admin" ADD "reset_password_token" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" ADD "reset_password_token_created_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "reset_password_token" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "reset_password_token_created_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_22c893796028a1072135d82b0e" ON "admin" ("reset_password_token") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ee6419219542371563e0592db5" ON "users" ("reset_password_token") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ee6419219542371563e0592db5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_22c893796028a1072135d82b0e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "reset_password_token_created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "reset_password_token"`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" DROP COLUMN "reset_password_token_created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" DROP COLUMN "reset_password_token"`,
    );
  }
}
