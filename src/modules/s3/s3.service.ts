import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

export interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

@Injectable()
export class S3Service {
  private s3: S3Client;
  private bucket: string;
  private readonly logger = new Logger(S3Service.name);

  constructor(private configService: ConfigService) {
    this.bucket = this.configService.get<string>("AWS_S3_BUCKET")!;

    const region = this.configService.get<string>("AWS_REGION");
    const accessKeyId = this.configService.get<string>("AWS_ACCESS_KEY_ID");
    const secretAccessKey = this.configService.get<string>(
      "AWS_SECRET_ACCESS_KEY",
    );

    if (!region || !accessKeyId || !secretAccessKey) {
      throw new Error(
        "Missing required AWS configuration: AWS_REGION, AWS_ACCESS_KEY_ID, and AWS_SECRET_ACCESS_KEY must be set",
      );
    }

    this.s3 = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });
  }

  async uploadFile(
    file: MulterFile,
    relatedId: string,
  ): Promise<string | null> {
    const key = `documents/${relatedId}/${file.originalname}`;

    try {
      const s3Response = await this.s3.send(
        new PutObjectCommand({
          Bucket: this.bucket,
          Key: key,
          Body: file.buffer,
          ContentType: file.mimetype,
        }),
      );

      if (s3Response.$metadata.httpStatusCode === 200) {
        return `https://${this.bucket}.s3.amazonaws.com/${key}`;
      }

      this.logger.warn(
        `S3 upload failed with status ${s3Response.$metadata.httpStatusCode}`,
      );

      return null;
    } catch (error) {
      this.logger.error("Error uploading file to S3:", error);
      return null;
    }
  }
}
