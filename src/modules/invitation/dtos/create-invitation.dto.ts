import {
  Is<PERSON>mail,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>idate<PERSON><PERSON>,
} from "class-validator";

import { IsNotEmptyString } from "src/utils/decorators/is-not-empty-string.decorator";

export class CreateInvitationDto {
  @IsNotEmptyString()
  @IsEmail()
  email: string;

  @IsNotEmptyString()
  name: string;

  @IsOptional()
  @IsNotEmptyString()
  jobTitle?: string;

  @ValidateIf((body: CreateInvitationDto) => !body.companyId)
  @IsNotEmpty()
  @IsUUID(4)
  consultantId?: string;

  @ValidateIf((body: CreateInvitationDto) => !body.consultantId)
  @IsNotEmpty()
  @IsUUID(4)
  companyId?: string;
}
