import { MigrationInterface, QueryRunner } from "typeorm";

export class RiskAssessment1755001721640 implements MigrationInterface {
  name = "RiskAssessment1755001721640";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP CONSTRAINT "FK_a1b82854a865e224fda3d943ff6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP CONSTRAINT "FK_076be718c0b4e1b1aa714733333"`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_card_ai_response" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "impact_summary" character varying, "impact_fin_consideration" character varying, "impact_velocity" character varying, "impact_persistence" character varying, "likelihood_summary" character varying, "quant_likelihood" character varying, "qualit_likelihood" character varying, "impact_score" numeric NOT NULL DEFAULT '0', "likelihood_score" numeric NOT NULL DEFAULT '0', "control_score" numeric NOT NULL DEFAULT '0', "risk_card_id" uuid NOT NULL, CONSTRAINT "REL_24f8165cf005f8b01e92eff446" UNIQUE ("risk_card_id"), CONSTRAINT "PK_cc6fa2db8425973cab2143578af" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."risk_control_template_enum" AS ENUM('pharmacy', 'information_technologies', 'construction')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."risk_control_type_enum" AS ENUM('detective', 'corrective', 'preventive')`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_control" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "titleRes" character varying NOT NULL, "descriptionRes" character varying NOT NULL, "title" character varying NOT NULL, "description" character varying NOT NULL, "template" "public"."risk_control_template_enum" NOT NULL, "type" "public"."risk_control_type_enum" NOT NULL, "risk_card_id" uuid NOT NULL, CONSTRAINT "PK_0c0b5ec80fcba2700db341d7f1d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_card" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying NOT NULL, "description" character varying NOT NULL, "location" character varying, "driver" character varying, "impact_summary" character varying, "impact_fin_consideration" character varying, "impact_non_fin_consideration" character varying, "impact_velocity" character varying, "impact_persistence" character varying, "likelihood_summary" character varying, "quant_likelihood" character varying, "qualit_likelihood" character varying, "max_score" numeric NOT NULL, "impact_score" numeric NOT NULL DEFAULT '0', "likelihood_score" numeric NOT NULL DEFAULT '0', "control_score" numeric NOT NULL DEFAULT '0', "risk_group_id" uuid NOT NULL, CONSTRAINT "PK_698eca0afab9214a251811ed2aa" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_group" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "risk_assessment_id" uuid NOT NULL, CONSTRAINT "PK_282478a3622ee09b24ac365b3cf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."risk_assessment_template_enum" AS ENUM('pharmacy', 'information_technologies', 'construction')`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_assessment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying NOT NULL, "template" "public"."risk_assessment_template_enum" NOT NULL, "context_report_id" uuid NOT NULL, "company_id" uuid NOT NULL, "consultant_id" uuid NOT NULL, CONSTRAINT "REL_a66209f2f386b9b4f372164454" UNIQUE ("context_report_id"), CONSTRAINT "PK_864a35a16fcb683df546ac6134e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "related_risk_card" ("risk_card_id" uuid NOT NULL, "related_risk_card_id" uuid NOT NULL, CONSTRAINT "PK_f78a5a53da1075ffdc5dccc55c8" PRIMARY KEY ("risk_card_id", "related_risk_card_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_21fb5cceeda0fa97bccb7505a1" ON "related_risk_card" ("risk_card_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5065474652cddb25eeb761532d" ON "related_risk_card" ("related_risk_card_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP COLUMN "company_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP COLUMN "consultant_id"`,
    );
    await queryRunner.query(`ALTER TABLE "context_report" DROP COLUMN "title"`);
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP COLUMN "subtitle"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD CONSTRAINT "FK_24f8165cf005f8b01e92eff4463" FOREIGN KEY ("risk_card_id") REFERENCES "risk_card"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_control" ADD CONSTRAINT "FK_bccae97ca7acbf5eab8741efb65" FOREIGN KEY ("risk_card_id") REFERENCES "risk_card"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD CONSTRAINT "FK_b2b3e11f2a34787b59dfe914fd3" FOREIGN KEY ("risk_group_id") REFERENCES "risk_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_group" ADD CONSTRAINT "FK_9366425f2ee6bc847f81a6741a9" FOREIGN KEY ("risk_assessment_id") REFERENCES "risk_assessment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" ADD CONSTRAINT "FK_a66209f2f386b9b4f3721644541" FOREIGN KEY ("context_report_id") REFERENCES "context_report"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" ADD CONSTRAINT "FK_da472569be358768ea52e2926ff" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" ADD CONSTRAINT "FK_164bf36adc88795f699c0d951b7" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "related_risk_card" ADD CONSTRAINT "FK_21fb5cceeda0fa97bccb7505a1f" FOREIGN KEY ("risk_card_id") REFERENCES "risk_card"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "related_risk_card" ADD CONSTRAINT "FK_5065474652cddb25eeb761532d6" FOREIGN KEY ("related_risk_card_id") REFERENCES "risk_card"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "related_risk_card" DROP CONSTRAINT "FK_5065474652cddb25eeb761532d6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "related_risk_card" DROP CONSTRAINT "FK_21fb5cceeda0fa97bccb7505a1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" DROP CONSTRAINT "FK_164bf36adc88795f699c0d951b7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" DROP CONSTRAINT "FK_da472569be358768ea52e2926ff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_assessment" DROP CONSTRAINT "FK_a66209f2f386b9b4f3721644541"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_group" DROP CONSTRAINT "FK_9366425f2ee6bc847f81a6741a9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP CONSTRAINT "FK_b2b3e11f2a34787b59dfe914fd3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_control" DROP CONSTRAINT "FK_bccae97ca7acbf5eab8741efb65"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP CONSTRAINT "FK_24f8165cf005f8b01e92eff4463"`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD "subtitle" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD "title" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD "consultant_id" uuid NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD "company_id" uuid NOT NULL`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5065474652cddb25eeb761532d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_21fb5cceeda0fa97bccb7505a1"`,
    );
    await queryRunner.query(`DROP TABLE "related_risk_card"`);
    await queryRunner.query(`DROP TABLE "risk_assessment"`);
    await queryRunner.query(
      `DROP TYPE "public"."risk_assessment_template_enum"`,
    );
    await queryRunner.query(`DROP TABLE "risk_group"`);
    await queryRunner.query(`DROP TABLE "risk_card"`);
    await queryRunner.query(`DROP TABLE "risk_control"`);
    await queryRunner.query(`DROP TYPE "public"."risk_control_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."risk_control_template_enum"`);
    await queryRunner.query(`DROP TABLE "risk_card_ai_response"`);
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD CONSTRAINT "FK_076be718c0b4e1b1aa714733333" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD CONSTRAINT "FK_a1b82854a865e224fda3d943ff6" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
