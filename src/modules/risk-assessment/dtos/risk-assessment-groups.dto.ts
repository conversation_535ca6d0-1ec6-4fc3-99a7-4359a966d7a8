import { Expose, Type } from "class-transformer";

class RiskCardDto {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  location: string;

  @Expose()
  impactScore: number;

  @Expose()
  likelihoodScore: number;

  @Expose()
  controlScore: number;

  @Expose()
  generalScore: number;
}

class RiskGroupDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  @Type(() => RiskCardDto)
  riskCards: RiskCardDto[];
}

export class RiskAssessmentGroupsDto {
  @Expose()
  @Type(() => RiskGroupDto)
  groups: RiskGroupDto[];
}
