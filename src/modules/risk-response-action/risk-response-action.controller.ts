import { <PERSON>, Get, Param, ParseUUIDPipe } from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import {
  CurrentUser,
  RequestUser,
} from "@utils/decorators/current-user.decorator";
import { Role } from "@utils/roles/role";
import { Roles } from "@utils/roles/roles.decorator";

import { RiskAssessmentService } from "@risk-assessment/risk-assessment.service";

import { RiskResponseActionResponseDto } from "./dto/risk-response-action-response.dto";
import { RiskResponseActionService } from "./risk-response-action.service";

@Controller("risk-response-action")
export class RiskResponseActionController {
  constructor(
    private readonly riskResponseActionService: RiskResponseActionService,
    private readonly riskAssessmentService: RiskAssessmentService,
  ) {}

  @Get("/assessment/:id")
  @Roles(Role.Client, Role.Consultant)
  async findByRiskAssessment(
    @Param("id", new ParseUUIDPipe({ version: "4" })) riskAssessmentId: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ): Promise<RiskResponseActionResponseDto[]> {
    await this.riskAssessmentService.canAccess(riskAssessmentId, user);

    const actions =
      await this.riskResponseActionService.findByRiskAssessmentByMitigate(
        riskAssessmentId,
      );

    return plainToInstance(
      RiskResponseActionResponseDto,
      actions.map((action) => ({
        ...action,
        maxScore: action.riskCard.maxScore,
        generalScore: action.riskCard.generalScore,
        riskCardTitle: action.riskCard.title,
        riskCardId: action.riskCard.id,
      })),
      { excludeExtraneousValues: true },
    );
  }
}
