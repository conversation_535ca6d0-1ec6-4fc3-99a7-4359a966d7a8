import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { SectionController } from "./section.controller";
import { Section } from "./section.entity";
import { SectionService } from "./section.service";
import { ContextReport } from "../context-report/context-report.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Section, ContextReport])],
  controllers: [SectionController],
  providers: [SectionService],
  exports: [SectionService],
})
export class SectionModule {}
