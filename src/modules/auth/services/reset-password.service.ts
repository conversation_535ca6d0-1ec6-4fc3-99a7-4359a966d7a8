import { BadRequestException, Injectable } from "@nestjs/common";
import { render } from "@react-email/components";
import * as React from "react";

import ResetPasswordEmail from "src/emails/ResetPasswordEmail";
import { AdminService } from "src/modules/admin/admin.service";
import { UserService } from "src/modules/user/user.service";
import { urlSafeNanoId } from "src/utils/nanoid";

import { MailService } from "../../mail/mail.service";
import { ResetPasswordDto } from "../dtos/reset-password.dto";

const RESET_PASSWORD_TOKEN_DURATION = 60 * 60 * 1000; // 1 hour
const THROTTLE_EMAIL_REQUEST_TIMEOUT = 10 * 1000; // 10 seconds

export enum ResetPasswordTokenInvalidReason {
  TokenNotFound = "token_not_found",
  TokenExpired = "token_expired",
}

@Injectable()
export class ResetPasswordService {
  constructor(
    private userService: UserService,
    private adminService: AdminService,
    private mailService: MailService,
  ) {}

  async createResetPasswordToken(email: string, isAdmin: boolean) {
    const service = isAdmin ? this.adminService : this.userService;

    const token = urlSafeNanoId();

    const entity = await service.findByEmail(email);
    if (!entity) {
      return null;
    }

    // quietly throttle requesting new email for 10 seconds
    if (
      entity.resetPasswordTokenCreatedAt &&
      Date.now() - entity.resetPasswordTokenCreatedAt.getTime() <
        THROTTLE_EMAIL_REQUEST_TIMEOUT
    ) {
      return null;
    }

    const updatedEntity = await service.update(entity.id, {
      resetPasswordToken: token,
      resetPasswordTokenCreatedAt: new Date(),
    });

    await this.mailService.sendEmail({
      to: email,
      subject: "Reset your password on Eastward",
      body: await render(
        React.createElement(ResetPasswordEmail, {
          token,
          isAdmin,
        }),
      ),
    });

    return updatedEntity;
  }

  async validateResetPasswordToken(
    token: string,
    isAdmin: boolean,
  ): Promise<{
    isValid: boolean;
    reason?: ResetPasswordTokenInvalidReason;
  }> {
    const service = isAdmin ? this.adminService : this.userService;

    const entity = await service.findByResetPasswordToken(token);

    if (!entity || !entity.resetPasswordTokenCreatedAt) {
      return {
        isValid: false,
        reason: ResetPasswordTokenInvalidReason.TokenNotFound,
      };
    }

    if (
      entity.resetPasswordTokenCreatedAt.getTime() +
        RESET_PASSWORD_TOKEN_DURATION <
      Date.now()
    ) {
      return {
        isValid: false,
        reason: ResetPasswordTokenInvalidReason.TokenExpired,
      };
    }

    return { isValid: true };
  }

  async resetPassword(token: string, body: ResetPasswordDto, isAdmin: boolean) {
    const service = isAdmin ? this.adminService : this.userService;

    const res = await this.validateResetPasswordToken(token, isAdmin);

    if (!res.isValid) {
      throw new BadRequestException("Token is invalid.");
    }

    const entity = await service.findByResetPasswordTokenOrFail(token);

    return await service.update(entity.id, {
      password: body.password,
      resetPasswordToken: null,
      resetPasswordTokenCreatedAt: null,
    });
  }
}
