import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateContextReportSectionTables1751884865499
  implements MigrationInterface
{
  name = "CreateContextReportSectionTables1751884865499";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "context_report" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying, "subtitle" character varying, "is_locked_for_client" boolean NOT NULL DEFAULT true, "consultant_name" character varying NOT NULL DEFAULT 'Eastward', "risk_exercise" character varying NOT NULL DEFAULT 'Single Materiality Assessment', "company_id" uuid NOT NULL, "consultant_id" uuid NOT NULL, CONSTRAINT "PK_2a8cf2aeb0479a1d4bff0eac1ea" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "section" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying, "level" integer NOT NULL, "position" integer NOT NULL, "prompt" character varying, "response" character varying, "text" character varying, "cited_from" character varying, "context_report_id" uuid NOT NULL, "parent_id" uuid, CONSTRAINT "PK_3c41d2d699384cc5e8eac54777d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD "short_name" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD CONSTRAINT "FK_a1b82854a865e224fda3d943ff6" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" ADD CONSTRAINT "FK_076be718c0b4e1b1aa714733333" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "FK_5ca7baa8dbd3ef117ffe9ba37e0" FOREIGN KEY ("parent_id") REFERENCES "section"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" ADD CONSTRAINT "FK_3a494965455a658ced48dee14e8" FOREIGN KEY ("context_report_id") REFERENCES "context_report"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "FK_3a494965455a658ced48dee14e8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "section" DROP CONSTRAINT "FK_5ca7baa8dbd3ef117ffe9ba37e0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP CONSTRAINT "FK_076be718c0b4e1b1aa714733333"`,
    );
    await queryRunner.query(
      `ALTER TABLE "context_report" DROP CONSTRAINT "FK_a1b82854a865e224fda3d943ff6"`,
    );
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "short_name"`);
    await queryRunner.query(`DROP TABLE "section"`);
    await queryRunner.query(`DROP TABLE "context_report"`);
  }
}
