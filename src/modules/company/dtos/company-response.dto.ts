import { Expose, Transform } from "class-transformer";

import {
  Invitation,
  InvitationStatus,
} from "src/modules/invitation/invitation.entity";

import { Company } from "../company.entity";

interface CompanyUserDto {
  memberId: string | null;
  invitationId: string;
  name: string;
  jobTitle: string;
  avatar: string | null;
  status: InvitationStatus;
}

interface AssessmentResponseDto {
  id: string;
  title: string;
}

interface AssessmentGroupResponseDto {
  title: string;

  assessments: AssessmentResponseDto[];
}

export class CompanyResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  @Transform(
    ({ obj }: { obj: Company }) =>
      Object.values(
        obj.riskAssessments.reduce(
          (accumulator, { id, title, createdAt }) => {
            const year = new Date(createdAt).getFullYear().toString();

            if (!accumulator[year]) {
              accumulator[year] = { title: year, assessments: [] };
            }
            accumulator[year].assessments.push({ id, title });
            return accumulator;
          },
          {} as Record<string, AssessmentGroupResponseDto>,
        ),
      ).sort((a, b) => b.title.localeCompare(a.title)),
    { toClassOnly: true },
  )
  assessmentGroups: AssessmentGroupResponseDto[];

  @Expose()
  @Transform(
    ({ obj }: { obj: Company }) =>
      obj.invitations.map((invite: Invitation) => ({
        memberId: invite.createdConsultantAccount?.id || null,
        invitationId: invite.id,
        name: invite.createdConsultantAccount?.name ?? invite.name,
        jobTitle: invite.createdConsultantAccount?.jobTitle ?? invite.jobTitle,
        avatar: invite.createdConsultantAccount?.avatar || null,
        status: invite.status,
      })),
    {
      toClassOnly: true,
    },
  )
  members: CompanyUserDto[];
}
