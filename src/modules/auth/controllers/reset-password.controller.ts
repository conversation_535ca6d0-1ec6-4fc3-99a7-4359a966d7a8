import { Body, Controller, Get, Param, Post } from "@nestjs/common";

import { ParseNanoIDPipe } from "src/utils/nanoid";

import { DoNotRevalidateToken } from "../decorators/do-not-revalidate-token.decorator";
import { Public } from "../decorators/public.decorator";
import { ForgotPasswordDto } from "../dtos/forgot-password.dto";
import { ResetPasswordDto } from "../dtos/reset-password.dto";
import { ResetPasswordService } from "../services/reset-password.service";

@Controller("auth")
@DoNotRevalidateToken()
export class ResetPasswordController {
  constructor(private resetPasswordService: ResetPasswordService) {}

  @Post("user/forgot-password")
  @Public()
  async forgotPasswordUser(@Body() body: ForgotPasswordDto) {
    await this.resetPasswordService.createResetPasswordToken(body.email, false);
    return;
  }

  @Get("user/reset-password/validate/:token")
  @Public()
  async validateResetPasswordUser(
    @Param("token", ParseNanoIDPipe) token: string,
  ) {
    return await this.resetPasswordService.validateResetPasswordToken(
      token,
      false,
    );
  }

  @Post("user/reset-password/:token")
  @Public()
  async resetPasswordUser(
    @Param("token", ParseNanoIDPipe) token: string,
    @Body() body: ResetPasswordDto,
  ) {
    await this.resetPasswordService.resetPassword(token, body, false);
    return;
  }

  @Post("admin/forgot-password")
  @Public()
  async forgotPasswordAdmin(@Body() body: ForgotPasswordDto) {
    await this.resetPasswordService.createResetPasswordToken(body.email, true);
    return;
  }

  @Get("admin/reset-password/validate/:token")
  @Public()
  async validateResetPasswordAdmin(
    @Param("token", ParseNanoIDPipe) token: string,
  ) {
    return await this.resetPasswordService.validateResetPasswordToken(
      token,
      true,
    );
  }

  @Post("admin/reset-password/:token")
  @Public()
  async resetPasswordAdmin(
    @Param("token", ParseNanoIDPipe) token: string,
    @Body() body: ResetPasswordDto,
  ) {
    await this.resetPasswordService.resetPassword(token, body, true);
    return;
  }
}
