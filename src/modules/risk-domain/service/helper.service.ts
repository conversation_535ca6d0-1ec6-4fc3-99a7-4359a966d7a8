import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { AIService } from "src/modules/ai/ai.service";
import { JobService } from "src/modules/job/job.service";
import { RiskResponseAction } from "src/modules/risk-response-action/risk-response-action.entity";

import { RiskAssessment } from "../../risk-assessment/risk-assessment.entity";
import { RiskCardAiResponse } from "../../risk-card/risk-card-ai-response.entity";
import { RiskCard } from "../../risk-card/risk-card.entity";
import { RiskControl } from "../../risk-control/risk-control.entity";

@Injectable()
export class HelperService {
  private readonly logger = new Logger(HelperService.name);
  constructor(
    @InjectRepository(RiskAssessment)
    private readonly riskAssessmentRepository: Repository<RiskAssessment>,
    @InjectRepository(RiskCard)
    private readonly riskCardRepository: Repository<RiskCard>,
    @InjectRepository(RiskCardAiResponse)
    private readonly riskCardAiResponseRepository: Repository<RiskCardAiResponse>,
    @InjectRepository(RiskControl)
    private readonly riskControlRepository: Repository<RiskControl>,
    @InjectRepository(RiskResponseAction)
    private readonly riskResponseActionRepository: Repository<RiskResponseAction>,
    private readonly aiService: AIService,
    private readonly jobService: JobService,
  ) {}

  async findRiskAssessmentWithRiskCards(
    id: string,
  ): Promise<RiskAssessment | null> {
    return await this.riskAssessmentRepository.findOne({
      where: { id },
      relations: { company: true, riskGroups: { riskCards: true } },
    });
  }

  async ingestCompanyDocs(companyId: string) {
    const response = await this.aiService.request(
      "/ai/risk-assessment/ingest-docs",
      "POST",
      { company_id: companyId },
    );

    if (!response.success) {
      throw new Error("Failed to ingest company docs");
    }
  }

  async processRiskCards(
    riskCards: RiskCard[],
    companyId: string,
    jobId: string,
  ) {
    const results = [];

    for (const risk of riskCards) {
      if (await this.jobService.isCanceled(jobId)) {
        this.logger.warn("Job canceled. Stopping risk assessment generation.");
        return results;
      }

      try {
        const data = await this.aiService.request(
          "/ai/risk-assessment",
          "POST",
          { company_id: companyId },
          risk,
        );

        results.push(data);
      } catch (err) {
        this.logger.warn(
          `Failed to fetch risk assessment for card ${risk.id}`,
          err,
        );
      }
    }

    return results;
  }
  async saveRiskCardResults(results: any[], riskAssessment: any) {
    for (const result of results) {
      const riskCardAiResponse = await this.riskCardAiResponseRepository.save(
        this.riskCardAiResponseRepository.create({
          ...(result as RiskCardAiResponse),
          riskCard: { id: result.id },
        }),
      );

      if (!riskCardAiResponse) {
        continue;
      }

      await this.riskCardRepository.update(
        result.id,
        // eslint-disable-next-line unused-imports/no-unused-vars
        (({ riskCard, riskCardId, ...rest }) => rest)(riskCardAiResponse),
      );

      if (result.controls?.length) {
        await this.riskControlRepository.save(
          result.controls.map((control: any) =>
            this.riskControlRepository.create({
              titleRes: control.title,
              descriptionRes: control.description,
              template: riskAssessment.template,
              title: control.title,
              description: control.description,
              type: control.type,
              riskCardId: result.id,
            }),
          ),
        );
      }
      if (result.actions?.length) {
        await this.riskResponseActionRepository.save(
          result.actions.map((action: any) =>
            this.riskResponseActionRepository.create({
              titleRes: action.title,
              title: action.title,
              riskCardId: result.id,
            }),
          ),
        );
      }
    }
  }

  async cleanUpSummaries(companyId: string) {
    try {
      await this.aiService.request(
        `/ai/risk-assessment/summary/${companyId}`,
        "DELETE",
      );
    } catch (err) {
      this.logger.error("Error in deleting summaries", err);
    }
  }

  private delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
