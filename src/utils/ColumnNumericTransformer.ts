import { ValueTransformer } from "typeorm";

function isNullOrUndefined<T>(
  obj: T | null | undefined,
): obj is null | undefined {
  return typeof obj === "undefined" || obj === null;
}

export class ColumnNumericTransformer implements ValueTransformer {
  to(data?: number | null | undefined): number | null | undefined {
    return data;
  }

  from(data?: string | null): number | null {
    if (!isNullOrUndefined(data)) {
      const res = parseFloat(data);
      if (isNaN(res)) {
        return null;
      } else {
        return res;
      }
    }
    return null;
  }
}
