import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { Request } from "express";
import { Strategy } from "passport-jwt";

import { AdminService } from "src/modules/admin/admin.service";
import { UserService } from "src/modules/user/user.service";
import { Role } from "src/utils/roles/role";

import { HTTP_COOKIE_NAME } from "../controllers/auth.controller";

const USER_INFO_COOKIE_NAME = "eaw_user-info";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private adminService: AdminService,
    private userService: UserService,
  ) {
    super({
      jwtFromRequest: (req) => JwtStrategy.extractJwtFromCookie(req),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET!,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: { sub: string; isAdmin: boolean }) {
    if (payload.isAdmin) {
      const user = await this.adminService.findOrFail(
        payload.sub,
        new UnauthorizedException(),
      );

      return { user, role: Role.Admin };
    }

    let userInfo: {
      role: Role.Consultant | Role.Client;
      accountId: string;
    };
    try {
      userInfo = JSON.parse(req.cookies[USER_INFO_COOKIE_NAME]);

      if (
        !userInfo.role ||
        !userInfo.accountId ||
        !Object.values(Role).includes(userInfo.role)
      ) {
        throw new UnauthorizedException();
      }
    } catch {
      throw new UnauthorizedException();
    }

    const user = await this.userService.findOrFail(
      payload.sub,
      new UnauthorizedException(),
    );

    if (userInfo.role === Role.Client) {
      const clientAccount = user.clientAccounts.find(
        (account) => account.id === userInfo.accountId,
      );
      if (!clientAccount) {
        throw new UnauthorizedException();
      }
      return { user, role: Role.Client, clientAccount };
    }

    const consultantAccount = user.consultantAccounts.find(
      (account) => account.id === userInfo.accountId,
    );
    if (!consultantAccount) {
      throw new UnauthorizedException();
    }
    return { user, role: Role.Consultant, consultantAccount };
  }

  private static extractJwtFromCookie(req: Request): string | null {
    return req.cookies?.[HTTP_COOKIE_NAME];
  }
}
