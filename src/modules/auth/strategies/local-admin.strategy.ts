import {
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-local";

import { Admin } from "src/modules/admin/admin.entity";
import { User } from "src/modules/user/user.entity";

import { AuthService } from "../services/auth.service";

@Injectable()
export class LocalAdminStrategy extends PassportStrategy(
  Strategy,
  "local-admin",
) {
  constructor(private authService: AuthService) {
    super({ usernameField: "email" });
  }

  async validate(
    email: string,
    password: string,
  ): Promise<Omit<Admin | User, "password">> {
    const res = await this.authService.validateAdmin(email, password);

    if (res.entity) {
      return res.entity;
    }

    if (res.reason === "locked") {
      throw new ForbiddenException();
    }

    throw new UnauthorizedException();
  }
}
