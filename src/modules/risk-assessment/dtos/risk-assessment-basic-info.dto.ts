import { Expose, Transform } from "class-transformer";

import { RiskAssessment } from "../risk-assessment.entity";

export class RiskAssessmentBasicInfoDto {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  @Transform(({ obj }: { obj: RiskAssessment }) => obj.contextReport.id, {
    toClassOnly: true,
  })
  contextReportId: string;

  @Expose()
  @Transform(({ obj }: { obj: RiskAssessment }) => obj.company.id, {
    toClassOnly: true,
  })
  companyId: string;
}
