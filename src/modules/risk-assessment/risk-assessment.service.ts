import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { FindOptionsRelations, Repository } from "typeorm";

import { RequestUser } from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { CreateRiskAssessmentDto } from "./dtos/create-risk-assessment.dto";
import { RiskAssessment } from "./risk-assessment.entity";
import { CompanyService } from "../company/company.service";
import { ContextReportService } from "../context-report/context-report.service";
import { RiskGroup } from "../risk-group/risk-group.entity";

@Injectable()
export class RiskAssessmentService {
  constructor(
    @InjectRepository(RiskAssessment)
    private riskAssessmentRepository: Repository<RiskAssessment>,
    private contextReportService: ContextReportService,
    private companyService: CompanyService,
  ) {}

  // TODO permissions, (role & org affiliation)
  async create(
    createRiskAssessmentDto: CreateRiskAssessmentDto,
    user: RequestUser<Role.Consultant>,
  ) {
    const company = await this.companyService.find(
      createRiskAssessmentDto.companyId,
    );

    if (!company) {
      throw new NotFoundException("Company not found.");
    }

    const contextReport =
      await this.contextReportService.createDefaultContextReport(
        company.name,
        company.shortName || company.name,
      );

    return await this.riskAssessmentRepository.save(
      this.riskAssessmentRepository.create({
        title: createRiskAssessmentDto.title,
        template: createRiskAssessmentDto.template,
        company: { id: createRiskAssessmentDto.companyId },
        consultant: { id: user.consultantAccount.consultant.id },
        contextReport: { id: contextReport.id },
      }),
    );
  }

  async findGroups(
    id: string,
    { relations }: { relations?: FindOptionsRelations<RiskAssessment> } = {},
  ): Promise<RiskGroup[]> {
    const riskAssessment = await this.riskAssessmentRepository.findOne({
      where: { id },
      relations,
      order: {
        riskGroups: {
          createdAt: "ASC",
          riskCards: {
            createdAt: "ASC",
          },
        },
      },
    });

    if (!riskAssessment) {
      throw new NotFoundException("Risk assessment not found.");
    }

    return riskAssessment.riskGroups;
  }

  async find(
    id: string,
    { relations }: { relations?: FindOptionsRelations<RiskAssessment> } = {},
  ): Promise<RiskAssessment | null> {
    return await this.riskAssessmentRepository.findOne({
      where: { id },
      relations,
    });
  }

  async findOrFail(
    id: string,
    { relations }: { relations?: FindOptionsRelations<RiskAssessment> } = {},
  ): Promise<RiskAssessment> {
    const riskAssessment = await this.find(id, {
      relations,
    });

    if (!riskAssessment) {
      throw new NotFoundException("Risk assessment not found.");
    }

    return riskAssessment;
  }

  async canAccess(id: string, user: RequestUser) {
    const riskAssessment = await this.findOrFail(id, {
      relations: { contextReport: true, company: { consultant: true } },
    });

    let viewerCanAccess = false;
    switch (user.role) {
      case Role.Admin:
        viewerCanAccess = false;
        break;
      case Role.Client:
        viewerCanAccess =
          riskAssessment.company.id === user.clientAccount.company.id;
        break;
      case Role.Consultant:
        viewerCanAccess =
          riskAssessment.company.consultant.id ===
          user.consultantAccount.consultant.id;
        break;
    }

    if (!viewerCanAccess) {
      throw new ForbiddenException();
    }
  }

  async canManage(id: string, user: RequestUser) {
    const riskAssessment = await this.findOrFail(id, {
      relations: { contextReport: true, company: { consultant: true } },
    });

    let viewerCanManage = false;
    switch (user.role) {
      case Role.Admin:
        viewerCanManage = false;
        break;
      case Role.Client:
        viewerCanManage = false;
        break;
      case Role.Consultant:
        viewerCanManage =
          riskAssessment.company.consultant.id ===
          user.consultantAccount.consultant.id;
        break;
    }

    if (!viewerCanManage) {
      throw new ForbiddenException();
    }
  }
}
