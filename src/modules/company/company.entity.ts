import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  <PERSON>,
} from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { ColumnNumericTransformer } from "src/utils/ColumnNumericTransformer";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { ClientAccount } from "../client-account/client-account.entity";
import { Consultant } from "../consultant/consultant.entity";
import { Document } from "../document/document.entity";
import { Invitation } from "../invitation/invitation.entity";
import { Job } from "../job/job.entity";
import { RiskAssessment } from "../risk-assessment/risk-assessment.entity";

@Entity()
@Check("risk_max_score - risk_min_score >= 0")
@Check("(risk_max_score - risk_min_score) % risk_step = 0")
export class Company extends BaseEntity {
  @Column()
  name: string;

  @NullableStringColumn({ name: "logo_url" })
  logoUrl: string | null;

  @NullableStringColumn()
  website: string | null;

  @NullableStringColumn({ name: "short_name" })
  shortName: string | null;

  @Column({ default: 0, name: "risk_min_score" })
  riskMinScore: number;

  @Column({ default: 5, name: "risk_max_score" })
  riskMaxScore: number;

  @Column({
    type: "numeric",
    default: () => "0.25",
    name: "risk_step",
    transformer: new ColumnNumericTransformer(),
  })
  riskStep: number;

  @ManyToOne(() => Consultant, (consultant) => consultant.companies, {
    nullable: false,
  })
  @JoinColumn({ name: "consultant_id" })
  consultant: Consultant;

  @OneToMany(() => ClientAccount, (clientAccount) => clientAccount.company)
  clientAccounts: ClientAccount[];

  @OneToMany(() => Invitation, (invitation) => invitation.company)
  invitations: Invitation[];

  @OneToMany(() => Document, (document) => document.company)
  documents: Document[];

  @OneToMany(() => Job, (job) => job.company)
  jobs: Job[];

  @OneToMany(() => RiskAssessment, (riskAssessment) => riskAssessment.company)
  riskAssessments: RiskAssessment[];
}
