import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { ClientAccountController } from "./client-account.controller";
import { ClientAccount } from "./client-account.entity";
import { ClientAccountService } from "./client-account.service";
import { InvitationModule } from "../invitation/invitation.module";
import { User } from "../user/user.entity";
import { UserModule } from "../user/user.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([ClientAccount, User]),
    forwardRef(() => InvitationModule),
    UserModule,
  ],
  providers: [ClientAccountService],
  controllers: [ClientAccountController],
  exports: [ClientAccountService],
})
export class ClientAccountModule {}
