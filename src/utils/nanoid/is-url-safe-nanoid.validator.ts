import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from "class-validator";

export function IsUrlSafeNanoId(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isNanoID",
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          const nanoidRegex = /^[A-Za-z0-9]{21}$/;
          return typeof value === "string" && nanoidRegex.test(value);
        },
        defaultMessage(_args: ValidationArguments) {
          return `$property must be a valid url-safe NanoID (21 alphanumeric characters)`;
        },
      },
    });
  };
}
