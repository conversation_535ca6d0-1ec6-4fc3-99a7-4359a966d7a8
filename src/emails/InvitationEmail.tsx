import { Link, Preview } from "@react-email/components";
import * as React from "react";

import "dotenv/config";

import { Button } from "./components/Button";
import { EmailContainer } from "./components/EmailContainer";
import { Logo } from "./components/Logo";
import { Text } from "./components/Text";

interface InvitationEmailProps {
  invitedBy: string;
  invitationToken: string;
  organization: React.ComponentProps<typeof Logo>["organization"] & {
    name: string;
  };
}

export const InvitationEmail = ({
  organization,
  invitedBy,
  invitationToken,
}: InvitationEmailProps) => (
  <EmailContainer>
    <Preview>
      You have been invited to join {organization.name} on Eastward!
    </Preview>

    <Logo organization={organization} size={84} className="mx-auto" />

    <Text size="lg" weight="semibold" className="text-center mt-4">
      {organization.name}
    </Text>

    <Text size="sm" className="text-center mt-4 mx-8">
      {invitedBy} has invited you to access{" "}
      <Text as="span" size="sm" color="accent-700" weight="semibold">
        {organization.name}
      </Text>{" "}
      on{" "}
      <Text as="span" size="sm" color="accent-700" weight="semibold">
        Eastward
      </Text>
      .
    </Text>

    <Button
      text="Confirm Invite"
      className="mt-8"
      href={`${process.env.WEB_URL}/join/${invitationToken}`}
    />

    <Text size="sm" className="text-center mt-4">
      After you confirm your invitation you will be redirected to the Eastward
      platform where you will be able to complete your signup.
    </Text>

    <Text size="sm" className="text-center mt-4">
      Need support? Our team is happy to help you. Contact us at{" "}
      <Link href="mailto:<EMAIL>">
        <Text as="span" size="sm" color="accent-700" weight="semibold" isLink>
          <EMAIL>
        </Text>
      </Link>
    </Text>
  </EmailContainer>
);

InvitationEmail.PreviewProps = {
  organization: {
    id: "",
    name: "Route 2, LLC",
    logoUrl: null,
  },
  invitedBy: "John Doe",
  invitationToken: "not_a_valid_token",
} satisfies InvitationEmailProps;

export default InvitationEmail;
