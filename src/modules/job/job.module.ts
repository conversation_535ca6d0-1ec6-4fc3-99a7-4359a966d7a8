import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { JobReportProcessorService } from "./job-report-processor.service";
import { <PERSON>Controller } from "./job.controller";
import { Job } from "./job.entity";
import { JobService } from "./job.service";
import { AIModule } from "../ai/ai-module";
import { ContextReportModule } from "../context-report/context-report.module";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Job]),
    forwardRef(() => ContextReportModule),
    forwardRef(() => RiskAssessmentModule),
    forwardRef(() => RiskDomainModule),
    AIModule,
  ],
  controllers: [JobController],
  providers: [JobService, JobReportProcessorService],
  exports: [JobService],
})
export class JobModule {}
