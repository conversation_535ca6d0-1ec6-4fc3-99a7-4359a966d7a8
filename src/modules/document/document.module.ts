import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { DocumentsController } from "./document.controller";
import { Document } from "./document.entity";
import { DocumentService } from "./document.service";
import { CompanyModule } from "../company/company.module";
import { S3Module } from "../s3/s3.module";

@Module({
  imports: [TypeOrmModule.forFeature([Document]), S3Module, CompanyModule],
  controllers: [DocumentsController],
  providers: [DocumentService],
  exports: [DocumentService],
})
export class DocumentModule {}
