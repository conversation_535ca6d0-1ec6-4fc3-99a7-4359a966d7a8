import { Body, Controller, Post, Param } from "@nestjs/common";

import { ParseNanoIDPipe } from "src/utils/nanoid";

import { ClientAccount } from "./client-account.entity";
import { ClientAccountService } from "./client-account.service";
import { Public } from "../auth/decorators/public.decorator";
import { RegisterFromInvitationDto } from "../consultant-account/register-from-invitation.dto";

@Controller("client-account")
export class ClientAccountController {
  constructor(private readonly clientAccountService: ClientAccountService) {}

  @Public()
  @Post("from-invitation/:token")
  async registerFromInvitation(
    @Param("token", ParseNanoIDPipe) token: string,
    @Body() registerFromInvitationDto: RegisterFromInvitationDto,
  ): Promise<ClientAccount> {
    return await this.clientAccountService.registerFromInvitation(
      token,
      registerFromInvitationDto,
    );
  }

  @Public()
  @Post("accept-invitation/:token")
  async acceptInvitation(
    @Param("token", ParseNanoIDPipe) token: string,
  ): Promise<ClientAccount> {
    return await this.clientAccountService.acceptInvitation(token);
  }
}
