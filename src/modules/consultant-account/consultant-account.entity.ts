import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { Consultant } from "../consultant/consultant.entity";
import { Invitation } from "../invitation/invitation.entity";
import { Permission } from "../permission/permission.entity";
import { User } from "../user/user.entity";

@Entity()
export class ConsultantAccount extends BaseEntity {
  @Column()
  name: string;

  @NullableStringColumn()
  avatar: string | null;

  @NullableStringColumn({ name: "job_title" })
  jobTitle: string | null;

  @ManyToOne(() => User, (user) => user.consultantAccounts, {
    nullable: false,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => Consultant, (consultant) => consultant.consultantAccounts, {
    nullable: false,
  })
  @JoinColumn({ name: "consultant_id" })
  consultant: Consultant;

  @OneToMany(
    () => Invitation,
    (invitation) => invitation.createdByConsultantAccount,
  )
  createdInvitations: Invitation[];

  @OneToOne(
    () => Invitation,
    (invitation) => invitation.createdConsultantAccount,
  )
  invitation: Invitation;

  @ManyToMany(() => Permission, (permission) => permission.consultantAccounts)
  @JoinTable({ name: "consultant_account_permission" })
  permissions: Permission;
}
