import {
  <PERSON><PERSON>oad,
  Colum<PERSON>,
  En<PERSON><PERSON>,
  JoinColumn,
  <PERSON><PERSON>Table,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";

import { ClientAccount } from "@client-account/client-account.entity";

import { BaseEntity } from "src/utils/base.entity";
import { ColumnNumericTransformer } from "src/utils/ColumnNumericTransformer";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { RiskCardAiResponse } from "./risk-card-ai-response.entity";
import { RiskResponseType } from "./risk-response-type.enum";
import { RiskControl } from "../risk-control/risk-control.entity";
import { RiskGroup } from "../risk-group/risk-group.entity";
import { RiskResponseAction } from "../risk-response-action/risk-response-action.entity";

@Entity()
export class RiskCard extends BaseEntity {
  @Column()
  title: string;

  @Column()
  description: string;

  @NullableStringColumn()
  location: string | null;

  @NullableStringColumn()
  driver: string | null;

  @NullableStringColumn({ name: "impact_summary" })
  impactSummary: string | null;

  @NullableStringColumn({ name: "financial_impact" })
  financialImpact: string | null;

  @NullableStringColumn({ name: "non_financial_impact" })
  nonFinancialImpact: string | null;

  @NullableStringColumn({ name: "impact_velocity" })
  impactVelocity: string | null;

  @NullableStringColumn({ name: "impact_persistence" })
  impactPersistence: string | null;

  @NullableStringColumn({ name: "likelihood_summary" })
  likelihoodSummary: string | null;

  @NullableStringColumn({ name: "quant_likelihood" })
  quantLikelihood: string | null;

  @NullableStringColumn({ name: "qualit_likelihood" })
  qualitLikelihood: string | null;

  @Column({ type: "numeric", name: "max_score" })
  maxScore: number;

  @Column({
    type: "numeric",
    default: 0,
    name: "impact_score",
    transformer: new ColumnNumericTransformer(),
  })
  impactScore: number;

  @Column({
    type: "numeric",
    default: 0,
    name: "likelihood_score",
    transformer: new ColumnNumericTransformer(),
  })
  likelihoodScore: number;

  @Column({
    type: "numeric",
    default: 0,
    name: "control_score",
    transformer: new ColumnNumericTransformer(),
  })
  controlScore: number;

  generalScore: number;

  @NullableStringColumn({ name: "control_summary" })
  controlSummary: string | null;

  @ManyToOne(() => RiskGroup, (riskGroup) => riskGroup.riskCards)
  @JoinColumn({ name: "risk_group_id" })
  riskGroup: RiskGroup;

  @Column({ name: "risk_group_id", type: "uuid" })
  riskGroupId: string;

  @NullableStringColumn()
  department: string | null;

  @NullableStringColumn({
    type: "enum",
    enum: RiskResponseType,
    name: "risk_response_type",
  })
  riskResponseType: RiskResponseType;

  @OneToOne(
    () => RiskCardAiResponse,
    (riskCardAiResponse) => riskCardAiResponse.riskCard,
  )
  aiResponse: RiskCardAiResponse | null;

  @OneToMany(() => RiskControl, (riskControl) => riskControl.riskCard)
  riskControls: RiskControl[];

  @ManyToMany(() => RiskCard, (riskCard) => riskCard.relatedRiskCards)
  @JoinTable({
    name: "related_risk_card",
    joinColumn: {
      name: "risk_card_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "related_risk_card_id",
      referencedColumnName: "id",
    },
  })
  relatedRiskCards: RiskCard[];

  @ManyToOne(
    () => ClientAccount,
    (clientAccount) => clientAccount.ownedRiskCards,
    { nullable: true },
  )
  @JoinColumn({ name: "owner_id" })
  owner: ClientAccount | null;

  @OneToMany(() => RiskResponseAction, (action) => action.riskCard)
  riskResponseActions: RiskResponseAction[];

  @AfterLoad()
  calculateGeneralScore() {
    this.generalScore =
      (this.impactScore * this.likelihoodScore * this.controlScore) /
      this.maxScore;
  }
}
