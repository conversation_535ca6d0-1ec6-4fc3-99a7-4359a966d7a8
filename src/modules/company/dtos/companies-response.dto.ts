import { Expose, Transform, Type } from "class-transformer";

import { Company } from "../company.entity";

class CompanyDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  logoUrl: string | null;

  @Expose()
  @Transform(({ obj }: { obj: Company }) => obj.clientAccounts.length, {
    toClassOnly: true,
  })
  membersCount: number;

  // TODO proper count
  @Expose()
  @Transform(({ obj }: { obj: Company }) => obj.riskAssessments.length, {
    toClassOnly: true,
  })
  activeAssessmentCount: number;

  // TODO proper count
  @Expose()
  @Transform(({ obj }: { obj: Company }) => obj.riskAssessments.length, {
    toClassOnly: true,
  })
  completedAssessmentCount: number;
}

export class CompaniesResponseDto {
  @Expose()
  @Type(() => CompanyDto)
  companies: CompanyDto[];
}
