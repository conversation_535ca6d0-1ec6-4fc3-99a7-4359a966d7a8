import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON>any } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { Company } from "../company/company.entity";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";
import { Invitation } from "../invitation/invitation.entity";
import { RiskAssessment } from "../risk-assessment/risk-assessment.entity";

@Entity()
export class Consultant extends BaseEntity {
  @Column()
  name: string;

  @NullableStringColumn({ name: "logo_url" })
  logoUrl: string | null;

  @NullableStringColumn()
  website: string | null;

  @OneToMany(() => Company, (company) => company.consultant)
  companies: Company[];

  @OneToMany(
    () => ConsultantAccount,
    (consultantAccount) => consultantAccount.consultant,
  )
  consultantAccounts: ConsultantAccount[];

  @OneToMany(() => Invitation, (invitation) => invitation.consultant)
  invitations: Invitation[];

  @OneToMany(() => RiskAssessment, (riskAssessment) => riskAssessment.company)
  riskAssessments: RiskAssessment[];
}
