import {
  HttpException,
  Injectable,
  NotFoundException,
  ConflictException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { QueryDeepPartialEntity } from "typeorm/query-builder/QueryPartialEntity";

import { hashPassword } from "src/utils/password/password";

import { Admin } from "./admin.entity";
import { CreateAdminDto } from "./dtos/create-admin.dto";

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
  ) {}

  async findAll(): Promise<Admin[]> {
    return await this.adminRepository.find();
  }

  async create(createAdminDto: CreateAdminDto): Promise<Admin> {
    const { password, ...admin } = createAdminDto;

    const existingAdmin = await this.adminRepository.findOne({
      where: { email: createAdminDto.email },
    });

    if (existingAdmin) {
      throw new ConflictException("Admin already exists.");
    }

    const hashedPassword = await hashPassword(password);

    return await this.adminRepository.save({
      ...admin,
      password: hashedPassword,
    });
  }

  async update(
    id: string,
    updateAdminDto: QueryDeepPartialEntity<Admin> & { password?: string },
  ): Promise<Admin> {
    const { password, ...rest } = updateAdminDto;

    const admin = await this.adminRepository.findOne({ where: { id } });
    if (!admin) {
      throw new NotFoundException("Admin not found.");
    }

    const hashedPassword = password ? await hashPassword(password) : undefined;

    await this.adminRepository.update(
      { id },
      {
        ...rest,
        ...(password ? { password: hashedPassword } : {}),
      },
    );

    return await this.adminRepository.findOneOrFail({ where: { id } });
  }

  async updateLockout(id: string, failedLoginAttempts: number): Promise<void> {
    await this.adminRepository.update(
      { id },
      {
        failedLoginAttempts,
        lastFailedLoginAttempt: failedLoginAttempts === 0 ? null : new Date(),
      },
    );
  }

  async delete(id: string): Promise<void> {
    const admin = await this.adminRepository.findOne({ where: { id } });
    if (!admin) {
      throw new NotFoundException("Admin not found.");
    }

    await this.adminRepository.delete(id);
    return;
  }

  async find(id: string): Promise<Admin | null> {
    return await this.adminRepository.findOne({
      where: {
        id,
      },
    });
  }

  async findOrFail<T extends HttpException>(
    id: string,
    Exception?: T,
  ): Promise<Admin> {
    const admin = await this.find(id);

    if (!admin) {
      throw Exception ?? new NotFoundException("Admin not found.");
    }

    return admin;
  }

  async findByEmail(email: string): Promise<Admin | null> {
    return await this.adminRepository.findOne({
      where: {
        email,
      },
    });
  }

  async findByResetPasswordToken(token: string): Promise<Admin | null> {
    return await this.adminRepository.findOne({
      where: {
        resetPasswordToken: token,
      },
    });
  }

  async findByResetPasswordTokenOrFail(token: string): Promise<Admin> {
    const admin = await this.findByResetPasswordToken(token);

    if (!admin) {
      throw new NotFoundException("Admin not found.");
    }

    return admin;
  }
}
