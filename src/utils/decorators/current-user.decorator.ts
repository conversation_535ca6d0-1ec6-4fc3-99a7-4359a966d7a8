import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import type { Request as ExpressRequest } from "express";

import { Admin as AdminEntity } from "src/modules/admin/admin.entity";
import { ClientAccount } from "src/modules/client-account/client-account.entity";
import { ConsultantAccount } from "src/modules/consultant-account/consultant-account.entity";
import { User } from "src/modules/user/user.entity";

import { Role } from "../roles/role";

type Admin = { user: AdminEntity; role: Role.Admin };
type Client = { user: User; role: Role.Client; clientAccount: ClientAccount };
type Consultant = {
  user: User;
  role: Role.Consultant;
  consultantAccount: ConsultantAccount;
};

type BaseRequestUser = Consultant | Client | Admin;

type RoleMap = {
  admin: Admin;
  client: Client;
  consultant: Consultant;
};

export type RequestUser<R extends keyof RoleMap | undefined = undefined> =
  R extends keyof RoleMap ? RoleMap[R] : BaseRequestUser;

export const CurrentUser = createParamDecorator(
  (_data, ctx: ExecutionContext) => {
    const request: ExpressRequest = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
