import { Transform } from "class-transformer";
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from "class-validator";

function IsNotEmptyString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      name: "IsNotEmptyString",
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: {
        validate(value: any) {
          return typeof value === "string" && value.trim().length > 0;
        },
        defaultMessage(args: ValidationArguments) {
          if (typeof args.value === "string") {
            return "Please enter a valid value.";
          }
          return "This field is required";
        },
      },
    });

    Transform(({ value }) => value.trim())(object, propertyName);
  };
}

export { IsNotEmptyString };
