import { Tailwind, pixelBasedPreset } from "@react-email/components";
import * as React from "react";

export const TailwindSetup = ({ children }: { children: React.ReactNode }) => (
  <Tailwind
    config={{
      presets: [pixelBasedPreset],
      theme: {
        extend: {
          colors: {
            "accent-600": "#ff6b39",
            "accent-700": "#ff4f00",
            "accent-900": "#981a00",
            "blue-950": "#123d55",
            "dark-50": "#f2f2f2",
            "dark-400": "#999999",
            "dark-500": "#808080",
            "dark-600": "#666666",
            "dark-700": "#4d4d4d",
            "dark-800": "#333333",
            "dark-900": "#1f1f1f",
            "dark-950": "#0a0a0a",
            white: "#fff",
            "yellow-950": "#413900",
          },
        },
      },
    }}
  >
    {children}
  </Tailwind>
);
