import * as path from "path";

import * as dotenv from "dotenv";
import { DataSource } from "typeorm";

dotenv.config();

export const AppDataSource = new DataSource({
  type: "postgres",
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || "5432", 10),
  username: process.env.DB_USER,
  password: process.env.DB_PASS || "",
  database: process.env.DB_NAME,
  entities: [path.join(__dirname, "../**/*.entity.{ts,js}")],
  migrations: [path.join(__dirname, "migrations/*.{ts,js}")],
  synchronize: false,
});
