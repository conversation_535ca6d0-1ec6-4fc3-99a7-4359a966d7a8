import { <PERSON><PERSON><PERSON>, IsNotEmpty, <PERSON>UUID } from "class-validator";

import { IsNotEmptyString } from "src/utils/decorators/is-not-empty-string.decorator";
import { RiskAssessmentTemplate } from "src/utils/risk-assessment-template.enum";

export class CreateRiskAssessmentDto {
  @IsNotEmptyString()
  title: string;

  @IsEnum(RiskAssessmentTemplate)
  @IsNotEmpty()
  template: RiskAssessmentTemplate;

  @IsUUID(4)
  @IsNotEmpty()
  companyId: string;
}
