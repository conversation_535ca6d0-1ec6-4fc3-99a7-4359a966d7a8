import { Expose } from "class-transformer";
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from "class-validator";

import { IsNotEmptyString } from "@utils/decorators/is-not-empty-string.decorator";

import { ActionStatus } from "../action-status.enum";

export class RiskResponseActionResponseDto {
  @Expose()
  @IsNotEmptyString()
  title: string;

  @Expose()
  @IsString()
  @IsOptional()
  dueDate?: string;

  @Expose()
  @IsString()
  @IsOptional()
  assignee?: string;

  @Expose()
  @IsEnum(ActionStatus)
  status: ActionStatus;

  @Expose()
  @IsString()
  riskCardTitle: string;

  @Expose()
  generalScore: number;

  @Expose()
  maxScore: number;

  @Expose()
  @IsUUID(4)
  @IsNotEmpty()
  riskCardId: string;

  /**
   * @remarks ai generated code
   * Computed date value based on dueDate format.
   */
  @Expose()
  get dateValue(): string | null {
    if (!this.dueDate) {
      return null;
    }

    // Case 1: Full date "Jan 16, 2026" or "January 16, 2026"
    const fullDateMatch = /^([A-Za-z]+)\s+(\d{1,2}),\s*(\d{4})$/i;

    const f = this.dueDate.match(fullDateMatch);
    if (f) {
      const monthName = f[1].toLowerCase();
      const day = parseInt(f[2], 10);
      const year = parseInt(f[3], 10);
      const monthNames = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];
      const monthIndex = monthNames.findIndex((name) =>
        name.startsWith(monthName),
      );
      if (monthIndex !== -1) {
        const month = monthIndex + 1;
        return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
      }
    }

    // Case 2: Qx/YYYY
    const quarterMatch = /^Q([1-4])\/(\d{4})$/i;
    const q = this.dueDate.match(quarterMatch);
    if (q) {
      const quarter = parseInt(q[1], 10);
      const year = parseInt(q[2], 10);
      const month = quarter * 3;
      const lastDay = new Date(year, month, 0).getDate();
      return `${year}-${String(month).padStart(2, "0")}-${String(lastDay).padStart(2, "0")}`;
    }

    // Case 3: Month name format, e.g., "Sep 2025" or "September 2025"
    const monthMatch = /^([A-Za-z]{3,})\s+(\d{4})$/;
    const m = this.dueDate.match(monthMatch);
    if (m) {
      const monthName = m[1].toLowerCase();
      const year = parseInt(m[2], 10);
      const monthNames = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];
      const monthIndex = monthNames.findIndex((name) =>
        name.startsWith(monthName),
      );
      if (monthIndex !== -1) {
        const month = monthIndex + 1;
        const lastDay = new Date(year, month, 0).getDate();
        return `${year}-${String(month).padStart(2, "0")}-${String(lastDay).padStart(2, "0")}`;
      }
    }

    return null;
  }
}
