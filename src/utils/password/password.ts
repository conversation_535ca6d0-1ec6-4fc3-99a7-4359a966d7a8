import * as bcrypt from "bcrypt";

const BCRYPT_SALT_ROUNDS = 10;
// if ever changing BCRYPT_SALT_ROUNDS, update this to match
export const DUMMY_PASSWORD_HASH =
  "$2b$10$ObIg1MYT9N4JLasAbBWhXOoi2ZYjtCKQSLiMCJn4Vwg/PEwy1sYvmuu";

export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, BCRYPT_SALT_ROUNDS);
}

export async function validatePassword(
  password: string,
  hash: string,
): Promise<boolean> {
  return await bcrypt.compare(password, hash);
}
