import { Modu<PERSON> } from "@nestjs/common";
import { APP_GUARD, APP_INTERCEPTOR } from "@nestjs/core";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";

import { AdminModule } from "src/modules/admin/admin.module";
import { UserModule } from "src/modules/user/user.module";
import { RolesGuard } from "src/utils/roles/roles.guard";

import {
  AuthController,
  JWT_TOKEN_DURATION,
} from "./controllers/auth.controller";
import { ResetPasswordController } from "./controllers/reset-password.controller";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import { AuthService } from "./services/auth.service";
import { ResetPasswordService } from "./services/reset-password.service";
import { JwtStrategy } from "./strategies/jwt.strategy";
import { LocalAdminStrategy } from "./strategies/local-admin.strategy";
import { LocalUserStrategy } from "./strategies/local-user.strategy";
import { TokenRevalidationInterceptor } from "./token-revalidation.interceptor";
import { MailModule } from "../mail/mail.module";

@Module({
  imports: [
    UserModule,
    AdminModule,
    PassportModule,
    MailModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET!,
      signOptions: { expiresIn: JWT_TOKEN_DURATION },
    }),
  ],
  providers: [
    AuthService,
    ResetPasswordService,
    LocalUserStrategy,
    LocalAdminStrategy,
    JwtStrategy,
    { provide: APP_GUARD, useClass: JwtAuthGuard },
    { provide: APP_GUARD, useClass: RolesGuard },
    { provide: APP_INTERCEPTOR, useClass: TokenRevalidationInterceptor },
  ],

  controllers: [AuthController, ResetPasswordController],
  exports: [AuthService, ResetPasswordService],
})
export class AuthModule {}
