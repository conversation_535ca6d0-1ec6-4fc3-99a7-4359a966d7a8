import {
  Controller,
  Post,
  Body,
  UseInterceptors,
  UploadedFiles,
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";

import { DocumentService } from "./document.service";
import { MulterFile } from "../s3/s3.service";

@Controller("document")
export class DocumentsController {
  constructor(private readonly documentService: DocumentService) {}

  @Post("upload-multiple")
  // TODO add once frontend implements this
  // @Roles(Role.Consultant)
  @UseInterceptors(FilesInterceptor("files"))
  async uploadMultiple(
    @UploadedFiles() files: MulterFile[],
    @Body("companyId") companyId: string,
    // @CurrentUser() user: RequestUser<Role.Consultant>,
  ) {
    // TODO add once frontend implements this
    // await this.documentService.canCreate(user, companyId);

    return this.documentService.uploadMultiple(files, companyId);
  }
}
