import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from "typeorm";

import { NullableStringColumn } from "@utils/decorators/nullable-string.decorator";

import { RiskCard } from "@risk-card/risk-card.entity";

import { BaseEntity } from "src/utils/base.entity";

import { ActionStatus } from "./action-status.enum";

@Entity()
export class RiskResponseAction extends BaseEntity {
  @Column()
  title: string;

  @NullableStringColumn({ name: "title_res" })
  titleRes: string | null;

  @NullableStringColumn({ name: "due_date" })
  dueDate: string | null;

  @NullableStringColumn({ name: "due_date_res" })
  dueDateRes: string | null;

  @NullableStringColumn()
  assignee: string | null;

  @Column({
    type: "enum",
    enum: ActionStatus,
    default: ActionStatus.NotStarted,
  })
  status: ActionStatus;

  @ManyToOne(() => RiskCard, (riskCard) => riskCard.riskResponseActions, {
    eager: true,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "risk_card" })
  riskCard: RiskCard;
}
