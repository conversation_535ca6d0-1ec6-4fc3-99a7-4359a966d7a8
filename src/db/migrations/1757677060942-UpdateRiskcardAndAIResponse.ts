import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRiskcardAndAIResponse1757677060942
  implements MigrationInterface
{
  name = "UpdateRiskcardAndAIResponse1757677060942";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP COLUMN "impact_fin_consideration"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "impact_non_fin_consideration"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "impact_fin_consideration"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD "financial_impact" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD "non_financial_impact" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD "control_summary" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "financial_impact" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "non_financial_impact" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "control_summary" character varying`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum" RENAME TO "job_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum" AS ENUM('pending', 'in_progress', 'completed', 'failed', 'canceled', 'canceling_in_progress')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum" USING "status"::"text"::"public"."job_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(`DROP TYPE "public"."job_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum_old" AS ENUM('pending', 'in_progress', 'completed', 'failed')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum_old" USING "status"::"text"::"public"."job_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(`DROP TYPE "public"."job_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum_old" RENAME TO "job_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "control_summary"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "non_financial_impact"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "financial_impact"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP COLUMN "control_summary"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP COLUMN "non_financial_impact"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP COLUMN "financial_impact"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "impact_fin_consideration" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "impact_non_fin_consideration" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD "impact_fin_consideration" character varying`,
    );
  }
}
