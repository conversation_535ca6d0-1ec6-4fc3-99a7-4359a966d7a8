import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";

import { JobStatus } from "src/modules/job/enum/job-status.enum";
import { JobService } from "src/modules/job/job.service";

import { HelperService } from "./helper.service";

@Injectable()
export class RiskDomainService {
  private readonly logger = new Logger(RiskDomainService.name);

  constructor(
    private readonly helperService: HelperService,
    private readonly jobService: JobService,
  ) {}

  async updateRiskCardWithAIResponses(riskAssessmentId: string, jobId: string) {
    const riskAssessment =
      await this.helperService.findRiskAssessmentWithRiskCards(
        riskAssessmentId,
      );

    if (!riskAssessment) {
      throw new NotFoundException(
        `Risk Assessment ${riskAssessmentId} not found`,
      );
    }
    if (!riskAssessment.riskGroups?.length) {
      throw new NotFoundException(
        `No risk groups for Risk Assessment ${riskAssessmentId}`,
      );
    }

    const riskCards = riskAssessment.riskGroups.flatMap(
      (g) => g.riskCards || [],
    );
    if (!riskCards.length) {
      throw new NotFoundException(
        `No risk cards for Risk Assessment ${riskAssessmentId}`,
      );
    }

    try {
      await this.helperService.ingestCompanyDocs(riskAssessment.company.id);

      const results = await this.helperService.processRiskCards(
        riskCards,
        riskAssessment.company.id,
        jobId,
      );

      await this.helperService.saveRiskCardResults(results, riskAssessment);

      if (results.length > 0) {
        await this.jobService.update(jobId, {
          status: JobStatus.Completed,
          result: {
            message: "Risk assessment has been successfully generated.",
          },
        });
      }

      return results;
    } catch (err) {
      this.logger.error("Error in generating risk assessment", err);
      throw new HttpException(
        "Error in generating risk assessment",
        HttpStatus.BAD_GATEWAY,
      );
    } finally {
      await this.helperService.cleanUpSummaries(riskAssessment.company.id);
    }
  }
}
