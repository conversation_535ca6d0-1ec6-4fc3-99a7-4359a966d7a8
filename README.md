# EASTWARD API

This is a backend API project for Eastward app built with:

- NestJS 11
- Postgres 17

## Prerequisite

- Node 22x
- [Docker Desktop](https://www.docker.com/products/docker-desktop)

## Project setup

- create .env file and copy values from 1Password
- In the root directory of the project execute:

```bash
$ npm install
```

## Compile and run the DB

- Make sure that Docker Desktop is open and running
- In the new terminal, in the root of the project execute:

```bash
$ docker compose up --build
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

### Creating an Admin user

Create the migration:

```bash
$ npm run migration:admin -- -f <first-name> -l <last-name> -p <password> -e <email>
```

Run the migration:

```bash
$ npm run migration:run
```

### Branch naming

Branches should be named using the format: `<jira-ticket-key>/<feature-name>`

Example: `EAW-00/login`

### Commit messages

Commits should follow the format: `[<jira-ticket-key>] <commit-message>`

Example: `[EAW-00] Add login page`

### Merge PR

Use squash commits only when merging into the dev branch
