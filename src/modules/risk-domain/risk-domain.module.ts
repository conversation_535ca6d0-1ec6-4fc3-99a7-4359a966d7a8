import { HttpModule } from "@nestjs/axios";
import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { RiskAssessment } from "../risk-assessment/risk-assessment.entity";
import { RiskCard } from "../risk-card/risk-card.entity";
import { RiskGroup } from "../risk-group/risk-group.entity";
import { RiskDomainService } from "./service/risk-domain.service";
import { RiskCardAiResponse } from "../risk-card/risk-card-ai-response.entity";
import { RiskControl } from "../risk-control/risk-control.entity";
import { HelperService } from "./service/helper.service";
import { AIModule } from "../ai/ai-module";
import { JobModule } from "../job/job.module";
import { RiskResponseAction } from "../risk-response-action/risk-response-action.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RiskAssessment,
      RiskGroup,
      RiskCard,
      RiskCardAiResponse,
      RiskControl,
      RiskResponseAction,
    ]),
    HttpModule,
    AIModule,
    JobModule,
  ],
  providers: [RiskDomainService, HelperService],
  exports: [RiskDomainService],
})
export class RiskDomainModule {}
