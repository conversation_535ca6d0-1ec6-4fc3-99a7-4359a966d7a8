import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { RiskGroupController } from "./risk-group.controller";
import { RiskGroup } from "./risk-group.entity";
import { RiskGroupService } from "./risk-group.service";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([RiskGroup]),
    RiskAssessmentModule,
    RiskDomainModule,
  ],
  providers: [RiskGroupService],

  controllers: [RiskGroupController],
  exports: [RiskGroupService],
})
export class RiskGroupModule {}
