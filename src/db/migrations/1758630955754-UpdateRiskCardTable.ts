import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRiskCardTable1758630955754 implements MigrationInterface {
  name = "UpdateRiskCardTable1758630955754";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "department" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "risk_card" ADD "owner_id" uuid`);
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD CONSTRAINT "FK_80a3152543103173472450ca856" FOREIGN KEY ("owner_id") REFERENCES "client_account"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP CONSTRAINT "FK_80a3152543103173472450ca856"`,
    );
    await queryRunner.query(`ALTER TABLE "risk_card" DROP COLUMN "owner_id"`);
    await queryRunner.query(`ALTER TABLE "risk_card" DROP COLUMN "department"`);
  }
}
