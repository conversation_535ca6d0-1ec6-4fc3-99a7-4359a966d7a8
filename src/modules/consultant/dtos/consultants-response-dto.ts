import { Expose, Transform, Type } from "class-transformer";

import { Consultant } from "../consultant.entity";

class ConsultantDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  logoUrl: string;

  @Expose()
  @Transform(({ obj }: { obj: Consultant }) => obj.companies.length, {
    toClassOnly: true,
  })
  companiesCount: number;

  @Expose()
  @Transform(
    ({ obj }: { obj: Consultant }) =>
      obj.companies.reduce(
        (accumulator, company) => accumulator + company.riskAssessments.length,
        0,
      ),
    { toClassOnly: true },
  )
  assessmentsCount: number;
}

export class ConsultantsListDto {
  @Expose()
  @Type(() => ConsultantDto)
  consultants: ConsultantDto[];
}
