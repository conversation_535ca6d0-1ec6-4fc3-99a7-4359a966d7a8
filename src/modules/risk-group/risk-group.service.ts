import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { CreateRiskGroupDto } from "./dtos/create-risk-group.dto";
import { UpdateRiskGroupDto } from "./dtos/update-risk-group.dto";
import { RiskGroup } from "./risk-group.entity";
import { defaultRiskGroups } from "../risk-assessment/default-risk-groups";
import { RiskAssessmentService } from "../risk-assessment/risk-assessment.service";

@Injectable()
export class RiskGroupService {
  constructor(
    @InjectRepository(RiskGroup)
    private riskGroupRepository: Repository<RiskGroup>,
    private riskAssessmentService: RiskAssessmentService,
  ) {}

  async create(createRiskGroupDto: CreateRiskGroupDto): Promise<RiskGroup> {
    await this.riskAssessmentService.findOrFail(
      createRiskGroupDto.riskAssessmentId,
    );

    return await this.riskGroupRepository.save(
      this.riskGroupRepository.create({
        name: createRiskGroupDto.name,
        riskAssessment: { id: createRiskGroupDto.riskAssessmentId },
      }),
    );
  }

  async find(id: string): Promise<RiskGroup | null> {
    return await this.riskGroupRepository.findOne({
      where: { id },
      relations: {
        riskAssessment: {
          company: true,
        },
      },
    });
  }

  async findOrFail(id: string): Promise<RiskGroup> {
    const riskGroup = await this.find(id);

    if (!riskGroup) {
      throw new NotFoundException("Risk group not found.");
    }

    return riskGroup;
  }

  async update(
    id: string,
    updateRiskGroupDto: UpdateRiskGroupDto,
  ): Promise<RiskGroup> {
    await this.findOrFail(id);

    const { riskAssessmentId, ...rest } = updateRiskGroupDto;

    if (riskAssessmentId) {
      await this.riskAssessmentService.findOrFail(riskAssessmentId);
    }

    await this.riskGroupRepository.update(
      { id },
      {
        ...rest,
        ...(riskAssessmentId
          ? { riskAssessment: { id: riskAssessmentId } }
          : {}),
      },
    );

    return await this.findOrFail(id);
  }

  async createDefaultGroups(riskAssessmentId: string): Promise<RiskGroup[]> {
    const createdRiskGroups = [];
    for (const riskGroup of defaultRiskGroups) {
      await this.riskAssessmentService.findOrFail(riskAssessmentId);

      const createdRiskGroup = await this.riskGroupRepository.save(
        this.riskGroupRepository.create({
          name: riskGroup,
          riskAssessment: { id: riskAssessmentId },
        }),
      );

      if (createdRiskGroup) {
        createdRiskGroups.push(createdRiskGroup);
      }
    }
    return createdRiskGroups;
  }

  async findByRiskAssessment(id: string): Promise<RiskGroup[] | null> {
    return await this.riskGroupRepository.find({
      where: { riskAssessmentId: id },
      relations: { riskAssessment: true },
      select: {
        id: true,
        riskAssessment: {
          id: true,
        },
        name: true,
      },
    });
  }
}
