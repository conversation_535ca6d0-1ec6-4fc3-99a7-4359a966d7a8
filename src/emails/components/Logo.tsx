import { Container, Img } from "@react-email/components";
import * as React from "react";
import type { ComponentPropsWithRef } from "react";

import { Text } from "./Text";
import { mcn, colorFromString, initialsFromString } from "./utils";

type LogoOrganization = {
  id: string;
  logoUrl?: string | null;
  name: string;
};

interface LogoProps extends ComponentPropsWithRef<typeof Container> {
  isSquare?: boolean;
  organization: LogoOrganization;
  size: number;
}

export const Logo = ({
  className,
  isSquare,
  organization,
  size,
  style,
  ...props
}: LogoProps) => {
  const initials = initialsFromString(organization.name);

  return (
    <Container
      {...props}
      className={mcn(!isSquare && "rounded-full", className)}
      style={{
        backgroundColor: organization.logoUrl
          ? undefined
          : colorFromString(organization.id),
        height: `${size}px`,
        width: `${size}px`,
        ...style,
      }}
    >
      {organization.logoUrl ? (
        <Img
          alt={""}
          className={mcn("object-cover", !isSquare && "rounded-full")}
          src={organization.logoUrl}
          style={{
            height: `${size}px`,
            width: `${size}px`,
          }}
        />
      ) : (
        <Text
          color="white"
          isUppercase
          size="xs"
          weight="semibold"
          className="block text-center"
        >
          {initials}
        </Text>
      )}
    </Container>
  );
};
