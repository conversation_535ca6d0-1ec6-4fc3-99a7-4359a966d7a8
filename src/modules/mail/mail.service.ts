import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

export interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

@Injectable()
export class MailService {
  private environment: string;

  constructor(private configService: ConfigService) {
    this.environment = this.configService.get<string>("NODE_ENV")!;
  }

  async sendEmail(config: {
    to: string;
    subject: string;
    body: string;
  }): Promise<void> {
    if (this.environment === "development") {
      // eslint-disable-next-line no-console
      console.log(`Sending email: ${config.subject} to ${config.to}`);
      // eslint-disable-next-line no-console
      console.log(config.body);

      return;
    }

    // TODO send
    // eslint-disable-next-line no-console
    console.log(`Sending email: ${config.subject} to ${config.to}`);
    // eslint-disable-next-line no-console
    console.log(config.body);
  }
}
