import { Injectable } from "@nestjs/common";

import { AdminService } from "src/modules/admin/admin.service";
import { RiskAssessmentTemplate } from "src/utils/risk-assessment-template.enum";
import { Role } from "src/utils/roles/role";

import { DemoSeedSerivice } from "./demo-seed.service";
import { Admin } from "../admin/admin.entity";
import { AuthService } from "../auth/services/auth.service";
import { ClientAccountService } from "../client-account/client-account.service";
import { CompanyService } from "../company/company.service";
import { ConsultantService } from "../consultant/consultant.service";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";
import { ConsultantAccountService } from "../consultant-account/consultant-account.service";
import { Invitation } from "../invitation/invitation.entity";
import { InvitationService } from "../invitation/invitation.service";
import { RiskAssessmentService } from "../risk-assessment/risk-assessment.service";

const COMMON_PASSWORD = "devdevdev";

const admins = [
  {
    email: "<EMAIL>",
    firstName: "Root",
    lastName: "McAdmin",
  },
];

const consultantUsers = [
  {
    email: "<EMAIL>",
    name: "Kristina Čolak",
    jobTitle: "Quantum Project Manager",
  },
  {
    email: "<EMAIL>",
    name: "Mark Marić",
    jobTitle: "Šalač betona",
  },
  {
    email: "<EMAIL>",
    name: "Emma Davis",
    jobTitle: "Senior Consultant",
  },
];

const companies = [
  {
    name: "Ars Futura",
    website: "https://arsfutura.co",
    clients: [
      {
        email: "<EMAIL>",
        name: "Mark",
        jobTitle: "V.D. upravljanja mješalicom",
      },
      {
        email: "<EMAIL>",
        name: "Mia",
        jobTitle: "Real Time Consultant",
      },
      {
        email: "<EMAIL>",
        name: "Tin",
        jobTitle: "Chief Oryx Wrangler",
      },
    ],
  },
  {
    name: "PontaHR d.o.o.",
    website: "https://pontahr.com",
    clients: [
      {
        email: "<EMAIL>",
        name: "Mia Matić",
        jobTitle: "Time Traveler Consultant",
      },
      {
        email: "<EMAIL>",
        name: "Tin Prvčić",
        jobTitle: "Chief Unicorn Wrangler",
      },
    ],
  },
  {
    name: "VitalisRx",
    website: "https://vitalisrx.com",
    clients: [
      {
        email: "<EMAIL>",
        name: "William Jones",
        jobTitle: "Pharmacy Operations Analyst",
      },
      {
        email: "<EMAIL>",
        name: "Jonathan Keller",
        jobTitle: "VP of Regulatory Affairs",
      },
      {
        email: "<EMAIL>",
        name: "Michael Hayes",
        jobTitle: "Director of Global Regulatory Compliance",
      },
      {
        email: "<EMAIL>",
        name: "Emily Carrington",
        jobTitle: "Director of Sustainability & ESG",
      },
      {
        email: "<EMAIL>",
        name: "Jessica Harper",
        jobTitle: "ESG Program Manager",
      },
    ],
  },
];

@Injectable()
export class SeedService {
  constructor(
    private authService: AuthService,
    private adminService: AdminService,
    private consultantService: ConsultantService,
    private companyService: CompanyService,
    private invitationService: InvitationService,
    private consultantAccountService: ConsultantAccountService,
    private clientAccountService: ClientAccountService,
    private riskAssessmentService: RiskAssessmentService,
    private readonly demoSeedService: DemoSeedSerivice,
  ) {}

  createdAdmins: Admin[] = [];

  async seed() {
    for (const admin of admins) {
      this.createdAdmins.push(
        await this.adminService.create({
          email: admin.email,
          password: COMMON_PASSWORD,
          firstName: admin.firstName,
          lastName: admin.lastName,
        }),
      );
    }

    const consultant = await this.consultantService.create({
      name: "Route2 LLC",
      website: "https://route2.com",
    });

    const requestUser = await this.authService.getRequestUser<Role.Admin>({
      email: this.createdAdmins[0].email,
      role: Role.Admin,
    });

    const consultantInvitations: Invitation[] = [];
    for (const consultantUser of consultantUsers) {
      consultantInvitations.push(
        await this.invitationService.create(requestUser, {
          email: consultantUser.email,
          name: consultantUser.name,
          jobTitle: consultantUser.jobTitle,
          consultantId: consultant.id,
        }),
      );
    }

    const consultantAccounts: ConsultantAccount[] = [];
    for (const invitation of consultantInvitations) {
      const result = await this.invitationService.validateByToken(
        invitation.token,
      );

      consultantAccounts.push(
        result.existingUser
          ? await this.consultantAccountService.acceptInvitation(
              invitation.token,
            )
          : await this.consultantAccountService.registerFromInvitation(
              invitation.token,
              {
                password: COMMON_PASSWORD,
                confirmPassword: COMMON_PASSWORD,
              },
            ),
      );
    }

    for (const [index, company] of companies.entries()) {
      const requestUser =
        await this.authService.getRequestUser<Role.Consultant>({
          email: consultantAccounts[index].user.email,
          role: Role.Consultant,
          accountId: consultantAccounts[index].id,
        });

      const createdCompany = await this.companyService.create(
        {
          name: company.name,
          website: company.website,
        },
        requestUser,
      );

      await this.riskAssessmentService.create(
        {
          title: "2025 Risk Assessment Report",
          template: RiskAssessmentTemplate.Pharmacy,
          companyId: createdCompany.id,
        },
        requestUser,
      );

      const clientInvitations: Invitation[] = [];
      for (const client of company.clients) {
        clientInvitations.push(
          await this.invitationService.create(requestUser, {
            email: client.email,
            name: client.name,
            jobTitle: client.jobTitle,
            companyId: createdCompany.id,
          }),
        );
      }

      for (const invitation of clientInvitations) {
        const result = await this.invitationService.validateByToken(
          invitation.token,
        );

        result.existingUser
          ? await this.clientAccountService.acceptInvitation(invitation.token)
          : await this.clientAccountService.registerFromInvitation(
              invitation.token,
              {
                password: COMMON_PASSWORD,
                confirmPassword: COMMON_PASSWORD,
              },
            );
      }
    }
  }

  async demoSeed() {
    await this.demoSeedService.run();
  }
}
