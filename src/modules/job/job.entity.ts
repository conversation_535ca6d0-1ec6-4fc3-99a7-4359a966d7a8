import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";

import { JobStatus } from "./enum/job-status.enum";
import { Company } from "../company/company.entity";

@Entity()
export class Job extends BaseEntity {
  @Column({
    type: "jsonb",
    array: false,
  })
  input: Record<string, string>;

  @Column({
    type: "enum",
    enum: JobStatus,
    default: JobStatus.Pending,
  })
  status: JobStatus;

  @Column({
    type: "jsonb",
    array: false,
    nullable: true,
  })
  result: Record<string, string> | null;

  @ManyToOne(() => Company, (company) => company.jobs, {
    nullable: false,
  })
  @JoinColumn({ name: "company_id" })
  company: Company;
}
