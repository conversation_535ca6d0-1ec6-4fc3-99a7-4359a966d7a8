import { PickType } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsArray, ValidateNested } from "class-validator";

import { CreateInvitationDto } from "./create-invitation.dto";

class BatchCreateMember extends PickType(CreateInvitationDto, [
  "email",
  "name",
  "jobTitle",
]) {}

export class BatchCreateInvitationDto extends PickType(CreateInvitationDto, [
  "consultantId",
  "companyId",
]) {
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => BatchCreateMember)
  members: BatchCreateMember[];
}
