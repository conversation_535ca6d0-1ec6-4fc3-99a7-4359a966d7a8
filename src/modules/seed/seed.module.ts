import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";

import { RiskGroupModule } from "@risk-group/risk-group.module";

import { RiskCardModule } from "@risk-card/risk-card.module";

import { ClassSerializerFactory } from "src/app.module";
import { AdminModule } from "src/modules/admin/admin.module";
import { ValidationPipeFactory } from "src/utils/validation-pipe.factory";

import { DemoSeedSerivice } from "./demo-seed.service";
import { SeedService } from "./seed.service";
import { AuthModule } from "../auth/auth.module";
import { ClientAccountModule } from "../client-account/client-account.module";
import { CompanyModule } from "../company/company.module";
import { ConsultantModule } from "../consultant/consultant.module";
import { ConsultantAccountModule } from "../consultant-account/consultant-account.module";
import { ContextReportModule } from "../context-report/context-report.module";
import { InvitationModule } from "../invitation/invitation.module";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskResponseActionModule } from "../risk-response-action/risk-response-action.module";
import { UserModule } from "../user/user.module";

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: "postgres",
        host: configService.get<string>("DB_HOST"),
        port: configService.get<number>("DB_PORT") || 5432,
        username: configService.get<string>("DB_USER"),
        password: configService.get<string>("DB_PASS") || "",
        database: configService.get<string>("DB_NAME"),
        entities: [__dirname + "/../**/*.entity.{ts,js}"],
        migrations: [__dirname + "/../db/migrations/*.{ts,js}"],
        synchronize: false,
        autoLoadEntities: true,
        migrationsRun: true,
      }),
    }),
    AuthModule,
    AdminModule,
    ConsultantModule,
    CompanyModule,
    InvitationModule,
    ConsultantAccountModule,
    ClientAccountModule,
    RiskAssessmentModule,
    UserModule,
    ContextReportModule,
    RiskGroupModule,
    RiskCardModule,
    RiskResponseActionModule,
  ],
  providers: [
    ValidationPipeFactory,
    ClassSerializerFactory,
    SeedService,
    DemoSeedSerivice,
  ],
  exports: [SeedService],
})
export class SeedModule {}
