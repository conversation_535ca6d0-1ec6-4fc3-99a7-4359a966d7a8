import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { RiskAssessmentController } from "./risk-assessment.controller";
import { RiskAssessment } from "./risk-assessment.entity";
import { RiskAssessmentService } from "./risk-assessment.service";
import { CompanyModule } from "../company/company.module";
import { ContextReportModule } from "../context-report/context-report.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([RiskAssessment]),
    forwardRef(() => ContextReportModule),
    CompanyModule,
    HttpModule,
    RiskDomainModule,
  ],
  providers: [RiskAssessmentService],

  controllers: [RiskAssessmentController],
  exports: [RiskAssessmentService],
})
export class RiskAssessmentModule {}
