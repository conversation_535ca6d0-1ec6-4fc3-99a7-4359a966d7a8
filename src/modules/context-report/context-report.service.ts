import { Injectable, NotFoundException, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { AIResponseDto } from "./ai-response.dto";
import { ContextReport } from "./context-report.entity";
import { getDefaultSections } from "./default-context-report-data";
import { Section } from "../section/section.entity";
import { SectionService } from "../section/section.service";

@Injectable()
export class ContextReportService {
  private readonly logger = new Logger(ContextReportService.name);

  constructor(
    @InjectRepository(ContextReport)
    private contextReportRepository: Repository<ContextReport>,
    private readonly sectionService: SectionService,
  ) {}

  async getByIdWithSectionTree(
    contextReportId: string,
  ): Promise<ContextReport> {
    const contextReport = await this.contextReportRepository.findOne({
      where: {
        id: contextReportId,
      },
      relations: { sections: true, riskAssessment: { company: true } },
    });

    if (!contextReport) {
      throw new NotFoundException(
        `Context report with ID ${contextReportId} not found`,
      );
    }

    contextReport.sections = await this.getSectionTree(contextReportId);
    return contextReport;
  }

  async findOne(contextReportId: string): Promise<ContextReport | null> {
    return await this.contextReportRepository.findOne({
      where: {
        id: contextReportId,
      },
      relations: {
        sections: true,
        riskAssessment: { company: true, consultant: true },
      },
    });
  }

  async findOneOrFail(contextReportId: string): Promise<ContextReport> {
    const contextReport = await this.findOne(contextReportId);

    if (!contextReport) {
      throw new NotFoundException();
    }

    return contextReport;
  }

  async createDefaultContextReport(
    companyName: string,
    companyShortName: string,
  ) {
    const contextReport = this.contextReportRepository.create();

    const savedContextReport =
      await this.contextReportRepository.save(contextReport);

    await this.sectionService.createMany(
      getDefaultSections(
        savedContextReport.id,
        companyName,
        companyShortName,
        savedContextReport.consultantName,
        savedContextReport.riskExercise,
      ),
    );

    return this.contextReportRepository.findOneOrFail({
      where: { id: savedContextReport.id },
      relations: ["sections"],
    });
  }

  private async getSectionTree(reportId: string): Promise<Section[]> {
    const roots =
      await this.sectionService.findByContextReportIdWithoutParent(reportId);

    for (const root of roots) {
      root.children = await this.loadChildrenRecursively(root.id, reportId);
    }

    return roots;
  }

  private async loadChildrenRecursively(
    parentId: string,
    reportId: string,
  ): Promise<Section[]> {
    const children = await this.sectionService.findByContextReportId(
      reportId,
      parentId,
    );

    for (const child of children) {
      child.children = await this.loadChildrenRecursively(child.id, reportId);
    }

    return children;
  }

  //TODO: When there will be multiple context report by company, this method wont be valid
  async findOneByCompanyId(companyId: string): Promise<ContextReport> {
    const contextReport = await this.contextReportRepository.findOne({
      where: {
        riskAssessment: { company: { id: companyId } },
      },
    });

    if (!contextReport) {
      throw new NotFoundException(
        `Context report with company ID ${companyId} not found`,
      );
    }
    return contextReport;
  }

  async updateSectionsWithAIResults(
    results: AIResponseDto[],
    contextId: string,
  ) {
    for (const result of results) {
      await this.sectionService.update(result.id, {
        text: result.content,
        response: result.content,
        citedFrom: result.citedFrom,
      });
    }
    return await this.getSectionTree(contextId);
  }
}
