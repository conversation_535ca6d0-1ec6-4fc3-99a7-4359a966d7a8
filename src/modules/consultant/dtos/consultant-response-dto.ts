import { Expose, Transform, Type } from "class-transformer";

import { Company } from "src/modules/company/company.entity";
import {
  Invitation,
  InvitationStatus,
} from "src/modules/invitation/invitation.entity";

export class ConsultantResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  @Type(() => CompanyDto)
  companies: CompanyDto[];

  // TODO if we ever figure out how to rename a dto field..
  @Expose()
  @Type(() => ConsultantUserDto)
  @Transform(
    ({ obj }) =>
      obj.invitations?.map((invite: Invitation) => ({
        memberId: invite.createdConsultantAccount?.id || null,
        invitationId: invite.id,
        name: invite.createdConsultantAccount?.name ?? invite.name,
        jobTitle: invite.createdConsultantAccount?.jobTitle ?? invite.jobTitle,
        avatar: invite.createdConsultantAccount?.avatar || null,
        status: invite.status,
      })),
    {
      toClassOnly: true,
    },
  )
  members: ConsultantUserDto[];
}

class CompanyDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  // TODO proper count
  @Expose()
  @Transform(({ obj }: { obj: Company }) => obj.riskAssessments.length, {
    toClassOnly: true,
  })
  activeAssessmentCount: number;

  // TODO proper count
  @Expose()
  @Transform(({ obj }: { obj: Company }) => obj.riskAssessments.length, {
    toClassOnly: true,
  })
  completedAssessmentCount: number;
}

class ConsultantUserDto {
  // TODO transform these ids, if this DTO is ever used
  @Expose()
  memberId: string | null;

  @Expose()
  invitationId: string;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) =>
      obj.createdConsultantAccount?.name ?? obj.name,
    {
      toClassOnly: true,
    },
  )
  name: string;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) =>
      obj.createdConsultantAccount?.jobTitle ?? obj.jobTitle,
    {
      toClassOnly: true,
    },
  )
  jobTitle: string | null;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) =>
      obj.createdConsultantAccount?.avatar || null,
    {
      toClassOnly: true,
    },
  )
  avatar: string | null;

  @Expose()
  @Transform(({ obj }: { obj: Invitation }) => obj.status, {
    toClassOnly: true,
  })
  status: InvitationStatus;
}
