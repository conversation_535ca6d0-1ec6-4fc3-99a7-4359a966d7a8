import {
  HttpException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { ClientAccount } from "./client-account.entity";
import { RegisterFromInvitationDto } from "../consultant-account/register-from-invitation.dto";
import { InvitationService } from "../invitation/invitation.service";
import { UserService } from "../user/user.service";

@Injectable()
export class ClientAccountService {
  constructor(
    private userService: UserService,
    private invitationService: InvitationService,
    @InjectRepository(ClientAccount)
    private clientAccountRepository: Repository<ClientAccount>,
  ) {}

  async registerFromInvitation(
    token: string,
    createFromInvitationDto: RegisterFromInvitationDto,
  ): Promise<ClientAccount> {
    const { isValid, reason, invitation } =
      await this.invitationService.validateByToken(token);

    if (!isValid || !invitation) {
      throw new UnprocessableEntityException(`Can't create user: ${reason}`);
    }

    if (!invitation.company) {
      throw new NotFoundException("Can't create user: Company not found.");
    }

    let user = await this.userService.findByEmail(invitation.email);

    if (user) {
      throw new UnprocessableEntityException("User already exists.");
    }

    user = await this.userService.createFromInvitation(
      invitation,
      createFromInvitationDto.password,
    );

    return await this.clientAccountRepository.save(
      this.clientAccountRepository.create({
        name: invitation.name,
        jobTitle: invitation.jobTitle,
        user: user,
        company: invitation.company,
        invitation,
      }),
    );
  }

  async acceptInvitation(token: string): Promise<ClientAccount> {
    const { isValid, reason, invitation } =
      await this.invitationService.validateByToken(token);

    if (!isValid || !invitation) {
      throw new UnprocessableEntityException(`Can't create user: ${reason}`);
    }

    if (!invitation.company) {
      throw new NotFoundException("Can't create user: Company not found.");
    }

    let user = await this.userService.findByEmail(invitation.email);

    if (!user) {
      throw new NotFoundException("User not found.");
    }

    return await this.clientAccountRepository.save(
      this.clientAccountRepository.create({
        name: invitation.name,
        jobTitle: invitation.jobTitle,
        user: user,
        company: invitation.company,
        invitation,
      }),
    );
  }

  async find(id: string): Promise<ClientAccount | null> {
    return await this.clientAccountRepository.findOne({
      where: {
        id,
      },
      relations: {
        user: true,
        company: true,
      },
    });
  }

  async findOrFail<T extends HttpException>(
    id: string,
    Exception?: T,
  ): Promise<ClientAccount> {
    const clientAccount = await this.find(id);

    if (!clientAccount) {
      throw Exception ?? new NotFoundException("Client account not found.");
    }

    return clientAccount;
  }
}
