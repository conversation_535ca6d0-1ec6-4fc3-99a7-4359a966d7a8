import { Body, Container, Head, Html } from "@react-email/components";
import * as React from "react";

import { TailwindSetup } from "./TailwindSetup";

export const EmailContainer = ({ children }: { children: React.ReactNode }) => (
  <Html>
    <Head />
    <TailwindSetup>
      <Body className="bg-dark-50 font-sans">
        <Container className="max-w-[542px] mx-auto my-[40px] rounded-2xl py-6 px-8 bg-white">
          {children}
        </Container>
      </Body>
    </TailwindSetup>
  </Html>
);
