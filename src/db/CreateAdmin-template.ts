import { MigrationInterface, QueryRunner } from "typeorm";

import { hashPassword } from "src/utils/password/password";

export class CreateAdmin__TIMESTAMP__ implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const passwordHash = await hashPassword("__PASSWORD__");

    await queryRunner.query(`
      INSERT INTO "admin" (email, password, first_name, last_name)
      VALUES ('__EMAIL__', '${passwordHash}', '__FIRST_NAME__', '__LAST_NAME__')
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM "admin" WHERE email = '__EMAIL__'
    `);
  }
}
