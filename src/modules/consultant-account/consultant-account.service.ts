import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { ConsultantAccount } from "./consultant-account.entity";
import { RegisterFromInvitationDto } from "./register-from-invitation.dto";
import { InvitationService } from "../invitation/invitation.service";
import { UserService } from "../user/user.service";

@Injectable()
export class ConsultantAccountService {
  constructor(
    private userService: UserService,
    private invitationService: InvitationService,
    @InjectRepository(ConsultantAccount)
    private consultantAccountRepository: Repository<ConsultantAccount>,
  ) {}

  async registerFromInvitation(
    token: string,
    createFromInvitationDto: RegisterFromInvitationDto,
  ): Promise<ConsultantAccount> {
    const { isValid, reason, invitation } =
      await this.invitationService.validateByToken(token);

    if (!isValid || !invitation) {
      throw new UnprocessableEntityException(`Can't create user: ${reason}`);
    }

    if (!invitation.consultant) {
      throw new NotFoundException("Can't create user: Consultant not found.");
    }

    let user = await this.userService.findByEmail(invitation.email);

    if (user) {
      throw new UnprocessableEntityException("User already exists.");
    }

    user = await this.userService.createFromInvitation(
      invitation,
      createFromInvitationDto.password,
    );

    return await this.consultantAccountRepository.save(
      this.consultantAccountRepository.create({
        name: invitation.name,
        jobTitle: invitation.jobTitle,
        user: user,
        consultant: invitation.consultant,
        invitation,
      }),
    );
  }

  async acceptInvitation(token: string): Promise<ConsultantAccount> {
    const { isValid, reason, invitation } =
      await this.invitationService.validateByToken(token);

    if (!isValid || !invitation) {
      throw new UnprocessableEntityException(`Can't create user: ${reason}`);
    }

    if (!invitation.consultant) {
      throw new NotFoundException("Can't create user: Consultant not found.");
    }

    let user = await this.userService.findByEmail(invitation.email);

    if (!user) {
      throw new NotFoundException("User not found.");
    }

    return await this.consultantAccountRepository.save(
      this.consultantAccountRepository.create({
        name: invitation.name,
        jobTitle: invitation.jobTitle,
        user: user,
        consultant: invitation.consultant,
        invitation,
      }),
    );
  }

  async find(id: string): Promise<ConsultantAccount | null> {
    return await this.consultantAccountRepository.findOne({
      where: {
        id,
      },
      relations: {
        user: true,
        consultant: true,
        invitation: true,
      },
    });
  }
}
