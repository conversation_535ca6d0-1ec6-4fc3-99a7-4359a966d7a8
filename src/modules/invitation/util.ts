import { urlSafeNanoId } from "src/utils/nanoid/urlSafeNanoId";

export const INVITATION_DURATION_MS = 72 * 60 * 60 * 1000;

/** default token duration is 72h */
export const generateInvitationExpiration = () => {
  return new Date(Date.now() + INVITATION_DURATION_MS);
};

/** generate a url safe, cryptographically secure token for the invitation */
export const generateInvitationToken = () => {
  return urlSafeNanoId();
};
