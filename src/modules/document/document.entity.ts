import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { Company } from "../company/company.entity";

@Entity()
export class Document extends BaseEntity {
  @Column()
  name: string;

  @NullableStringColumn({ name: "s3_url" })
  s3Url: string;

  @ManyToOne(() => Company, (company) => company.documents, {
    nullable: false,
  })
  @JoinColumn({ name: "company_id" })
  company: Company;
}
