import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { RiskAssessmentTemplate } from "src/utils/risk-assessment-template.enum";

import { RiskCard } from "../risk-card/risk-card.entity";

enum RiskControlType {
  Detective = "detective",
  Corrective = "corrective",
  Preventive = "preventive",
}

@Entity()
export class RiskControl extends BaseEntity {
  @Column()
  titleRes: string;

  @Column()
  descriptionRes: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column({
    type: "enum",
    enum: RiskAssessmentTemplate,
  })
  template: RiskAssessmentTemplate;

  @Column({
    type: "enum",
    enum: RiskControlType,
  })
  type: RiskControlType;

  @ManyToOne(() => RiskCard, (riskCard) => riskCard.riskControls)
  @JoinColumn({ name: "risk_card_id" })
  riskCard: RiskCard;

  @Column({ name: "risk_card_id", type: "uuid" })
  riskCardId: string;
}
