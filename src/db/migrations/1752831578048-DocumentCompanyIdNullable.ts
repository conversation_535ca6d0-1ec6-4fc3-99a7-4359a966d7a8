import { MigrationInterface, QueryRunner } from "typeorm";

export class DocumentCompanyIdNullable1752831578048
  implements MigrationInterface
{
  name = "DocumentCompanyIdNullable1752831578048";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document" DROP CONSTRAINT "FK_99f2963bedb48d019645f568cf0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" ALTER COLUMN "company_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" ADD CONSTRAINT "FK_99f2963bedb48d019645f568cf0" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document" DROP CONSTRAINT "FK_99f2963bedb48d019645f568cf0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" ALTER COLUMN "company_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "document" ADD CONSTRAINT "FK_99f2963bedb48d019645f568cf0" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
