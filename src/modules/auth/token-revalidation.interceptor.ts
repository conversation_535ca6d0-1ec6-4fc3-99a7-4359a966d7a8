import {
  CallHand<PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Request, Response } from "express";
import { Observable, tap } from "rxjs";

import { Role } from "src/utils/roles/role";

import {
  HTTP_COOKIE_CONFIG,
  HTTP_COOKIE_NAME,
} from "./controllers/auth.controller";
import { DO_NOT_REVALIDATE_TOKEN_KEY } from "./decorators/do-not-revalidate-token.decorator";
import { AuthService } from "./services/auth.service";

@Injectable()
export class TokenRevalidationInterceptor implements NestInterceptor {
  constructor(
    private authService: AuthService,
    private reflector: Reflector,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const req: Request = ctx.getRequest();
    const res: Response = ctx.getResponse();

    const doNotRevalidateToken = this.reflector.getAllAndOverride<boolean>(
      DO_NOT_REVALIDATE_TOKEN_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (doNotRevalidateToken) {
      return next.handle();
    }

    if (!req.user) {
      return next.handle();
    }

    return next.handle().pipe(
      tap(async () => {
        if (!req.user) {
          return;
        }

        const jwt = await this.authService.revalidateToken(
          req.user.user,
          req.user.role === Role.Admin,
        );

        res.cookie(HTTP_COOKIE_NAME, jwt, HTTP_COOKIE_CONFIG);

        return;
      }),
    );
  }
}
