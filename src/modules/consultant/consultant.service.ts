import { HttpException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { Consultant } from "./consultant.entity";
import { CreateConsultantDto } from "./dtos/create-consultant.dto";

@Injectable()
export class ConsultantService {
  constructor(
    @InjectRepository(Consultant)
    private consultantRepository: Repository<Consultant>,
  ) {}

  async create(createConsultantDto: CreateConsultantDto): Promise<Consultant> {
    return await this.consultantRepository.save(
      this.consultantRepository.create(createConsultantDto),
    );
  }

  async find(id: string): Promise<Consultant | null> {
    return await this.consultantRepository.findOne({
      where: { id },
      relations: {
        companies: {
          riskAssessments: true,
        },
        invitations: {
          createdConsultantAccount: true,
        },
      },
      order: {
        companies: {
          createdAt: "DESC",
        },
        invitations: {
          createdAt: "ASC",
        },
      },
    });
  }

  async findOrFail<T extends HttpException>(
    id: string,
    Exception?: T,
  ): Promise<Consultant> {
    const consultant = await this.find(id);

    if (!consultant) {
      throw Exception ?? new NotFoundException("Consultant not found.");
    }

    return consultant;
  }

  async findAll(): Promise<Consultant[]> {
    return await this.consultantRepository.find({
      relations: {
        companies: {
          riskAssessments: true,
        },
      },
      order: {
        createdAt: "DESC",
      },
    });
  }
}
