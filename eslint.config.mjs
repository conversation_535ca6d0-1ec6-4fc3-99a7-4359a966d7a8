import tsParser from "@typescript-eslint/parser";
import tsPlugin from "@typescript-eslint/eslint-plugin";
import importPlugin from "eslint-plugin-import";
import prettierPlugin from "eslint-plugin-prettier";
import js from "@eslint/js";
import globals from "globals";
import unusedImports from "eslint-plugin-unused-imports";

const pathGroups = [
  { pattern: "@config/**", group: "internal", position: "before" },
  { pattern: "@db/**", group: "internal", position: "before" },
  { pattern: "@emails/**", group: "internal", position: "before" },
  { pattern: "@utils/**", group: "internal", position: "before" },
  { pattern: "@user/**", group: "internal", position: "before" },
  { pattern: "@admin/**", group: "internal", position: "before" },
  { pattern: "@auth/**", group: "internal", position: "before" },
  {
    pattern: "@consultant-account/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@client-account/**",
    group: "internal",
    position: "before",
  },
  { pattern: "@company/**", group: "internal", position: "before" },
  {
    pattern: "@consultant/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@invitation/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@permission/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@context-report/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@risk-assessment/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@risk-domain/**",
    group: "internal",
    position: "before",
  },
  {
    pattern: "@risk-group/**",
    group: "internal",
    position: "before",
  },
  { pattern: "@risk-card/**", group: "internal", position: "before" },
  { pattern: "@section/**", group: "internal", position: "before" },
  { pattern: "@document/**", group: "internal", position: "before" },
  { pattern: "@job/**", group: "internal", position: "before" },
  { pattern: "@ai/**", group: "internal", position: "before" },
  { pattern: "@seed/**", group: "internal", position: "before" },
];

export default [
  js.configs.recommended,

  {
    files: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: "./tsconfig.json",
        tsconfigRootDir: process.cwd(),
        ecmaVersion: 2020,
        sourceType: "module",
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        ...globals.node,
        ...globals.browser,
        ...globals.jest,
      },
    },
    plugins: {
      "@typescript-eslint": tsPlugin,
      import: importPlugin,
      prettier: prettierPlugin,
      "unused-imports": unusedImports,
    },
    settings: {
      "import/resolver": {
        typescript: {
          project: "./tsconfig.json",
        },
      },
    },
    rules: {
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off",

      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: ".*",
        },
      ],

      curly: ["error", "all"],

      "@typescript-eslint/explicit-function-return-type": "off",
      "import/order": [
        "error",
        {
          groups: [
            "builtin",
            "external",
            "internal",
            ["parent", "sibling", "index"],
          ],
          pathGroups,
          pathGroupsExcludedImportTypes: ["builtin"],
          "newlines-between": "always",
          alphabetize: { order: "asc", caseInsensitive: true },
        },
      ],
      "no-multiple-empty-lines": [
        "error",
        {
          max: 1,
          maxEOF: 0,
          maxBOF: 0,
        },
      ],
      "prettier/prettier": ["error"],
      "no-console": "warn",
      "no-restricted-syntax": [
        "error",
        {
          selector: "ForInStatement",
          message:
            "for…in loops are forbidden. Use Object.keys(), Object.values(), or Object.entries() instead.",
        },
      ],
      "no-restricted-imports": [
        "warn",
        // {
        //   patterns: [
        //     {
        //       group: ["./*/**"],
        //       message:
        //         "Do not import from a nested path (e.g. ./risk/...). Instead, expose an index.ts and import from the parent folder.",
        //     },
        //   ],
        // },
      ],
    },
  },
];
