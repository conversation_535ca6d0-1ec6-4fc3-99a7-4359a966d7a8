import {
  BadRequestException,
  Injectable,
  UnprocessableEntityException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { render } from "@react-email/components";
import * as React from "react";
import { Repository } from "typeorm";

import InvitationEmail from "src/emails/InvitationEmail";
import { RequestUser } from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { CreateInvitationDto } from "./dtos/create-invitation.dto";
import { Invitation, InvitationStatus } from "./invitation.entity";
import { generateInvitationExpiration, generateInvitationToken } from "./util";
import { ClientAccount } from "../client-account/client-account.entity";
import { CompanyService } from "../company/company.service";
import { ConsultantService } from "../consultant/consultant.service";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";
import { BatchCreateInvitationDto } from "./dtos/batch-create-invitation.dto";
import { User } from "../user/user.entity";
import { UserService } from "../user/user.service";

export enum InvitationInvalidReason {
  InvitationNotFound = "invitation_not_found",
  UserExists = "user_exists",
  InvitationExpired = "invitation_expired",
  InvitationInvalid = "invitation_invalid",
}

export type ValidationResult = {
  isValid: boolean;
  reason: InvitationInvalidReason | null;
  invitation: Invitation | null;
  existingUser: User | null;
};

@Injectable()
export class InvitationService {
  constructor(
    @InjectRepository(Invitation)
    private invitationRepository: Repository<Invitation>,
    private consultantService: ConsultantService,
    private companyService: CompanyService,
    private userService: UserService,
    @InjectRepository(ConsultantAccount)
    private consultantAccountRepository: Repository<ConsultantAccount>,
    @InjectRepository(ClientAccount)
    private clientAccountRepository: Repository<ClientAccount>,
  ) {}

  async find(invitationId: string): Promise<Invitation | null> {
    return await this.invitationRepository.findOne({
      where: {
        id: invitationId,
      },
      relations: {
        createdByAdmin: true,
        createdByConsultantAccount: true,
      },
    });
  }

  async findByToken(token: string): Promise<Invitation | null> {
    return await this.invitationRepository.findOne({
      where: {
        token,
      },
      relations: {
        createdByAdmin: true,
        createdByConsultantAccount: true,
      },
    });
  }

  // TODO send email
  // TODO validate new user's role - whether it's possible for the org type
  // TODO validate if current user can create the invitation
  async create(
    currentUser: RequestUser,
    createInvitationDto: CreateInvitationDto,
  ): Promise<Invitation> {
    const { consultantId, companyId, ...invitationData } = createInvitationDto;

    if (consultantId) {
      const consultant = await this.consultantService.find(consultantId);
      if (!consultant) {
        throw new UnprocessableEntityException(
          `Consultant with id: ${consultantId} not found.`,
        );
      }
    }

    if (companyId) {
      const company = await this.companyService.find(companyId);
      if (!company) {
        throw new UnprocessableEntityException(
          `Company with id: ${companyId} not found.`,
        );
      }
    }

    // check for existing ACCOUNT with this consultant/company
    if (consultantId) {
      const consultantAccount = await this.consultantAccountRepository.findOne({
        relations: { user: true, consultant: true },
        where: {
          user: { email: invitationData.email },
          consultant: { id: consultantId },
        },
      });
      if (consultantAccount) {
        throw new UnprocessableEntityException(
          `User with email: ${invitationData.email} already has an account with this consultant.`,
        );
      }
    }

    if (companyId) {
      const clientAccount = await this.clientAccountRepository.findOne({
        relations: { user: true, company: true },
        where: {
          user: { email: invitationData.email },
          company: { id: companyId },
        },
      });
      if (clientAccount) {
        throw new UnprocessableEntityException(
          `User with email: ${invitationData.email} already has an account with this client.`,
        );
      }
    }

    // check for existing invitation
    const existingInvitationForOrganization = consultantId
      ? await this.invitationRepository.findOne({
          relations: { consultant: true },
          where: {
            email: invitationData.email,
            consultant: { id: consultantId },
          },
        })
      : await this.invitationRepository.findOne({
          relations: { company: true },
          where: {
            email: invitationData.email,
            company: { id: companyId },
          },
        });

    if (existingInvitationForOrganization) {
      throw new UnprocessableEntityException(
        `Invitation with email: ${invitationData.email} for this organization already exists.`,
      );
    }

    const organizationInfo = companyId
      ? { company: { id: companyId } }
      : { consultant: { id: consultantId } };

    const creatorInfo =
      currentUser.role === Role.Admin
        ? { createdByAdmin: { id: currentUser.user.id } }
        : currentUser.role === Role.Consultant
          ? {
              createdByConsultantAccount: {
                id: currentUser.consultantAccount.id,
              },
            }
          : {
              createdByClientAccount: {
                id: currentUser.clientAccount.id,
              },
            };

    return await this.invitationRepository.save(
      this.invitationRepository.create({
        ...invitationData,
        ...organizationInfo,
        ...creatorInfo,
      }),
    );
  }

  async batchCreate(
    currentUser: RequestUser,
    batchCreateInvitationDto: BatchCreateInvitationDto,
  ): Promise<void> {
    const { members, consultantId, companyId } = batchCreateInvitationDto;

    // check for existing members/invitations
    const memberErrors: Record<string, { email: string }> = {};
    for (const [index, member] of members.entries()) {
      const { exists, reason } = await this.invitationOrUserExists({
        ...member,
        consultantId,
        companyId,
      });

      if (exists) {
        memberErrors[index] = { email: reason };
      }
    }

    if (Object.keys(memberErrors).length > 0) {
      throw new BadRequestException({
        validationErrors: { members: memberErrors },
      });
    }

    for (const member of members) {
      await this.create(currentUser, {
        ...member,
        consultantId,
        companyId,
      });
    }

    return;
  }

  // TODO send email
  async renew(token: string): Promise<Invitation> {
    const { isValid, reason, invitation } = await this.validateByToken(token);

    // only allow renewing expired invitations (that are otherwise valid)
    if (isValid) {
      throw new UnprocessableEntityException(
        `Invitation with token: ${token} is still valid.`,
      );
    }

    if (!invitation || reason !== "invitation_expired") {
      throw new UnprocessableEntityException(
        `Invitation with token: ${token} is not valid: ${reason}.`,
      );
    }

    await this.invitationRepository.update(invitation.id, {
      expiresAt: generateInvitationExpiration(),
      token: generateInvitationToken(),
    });

    return await this.invitationRepository.findOneOrFail({
      where: {
        id: invitation.id,
      },
      relations: {
        createdByAdmin: true,
        createdByConsultantAccount: {
          consultant: true,
        },
        createdByClientAccount: {
          company: true,
        },
        consultant: true,
        company: true,
      },
    });
  }

  async renderInvitationEmail(invitation: Invitation): Promise<string> {
    const invitedByName = invitation.createdByAdmin
      ? invitation.createdByAdmin.firstName +
        " " +
        invitation.createdByAdmin.lastName
      : (invitation.createdByConsultantAccount?.consultant.name ||
          invitation.createdByClientAccount?.company.name)!;

    return await render(
      React.createElement(InvitationEmail, {
        organization: (invitation.company || invitation.consultant)!,
        invitedBy: invitedByName,
        invitationToken: invitation.token,
      }),
    );
  }

  async delete(invitationId: string): Promise<void> {
    await this.invitationRepository.delete(invitationId);
    return;
  }

  async validateByToken(invitationId: string): Promise<ValidationResult> {
    const invitation = await this.invitationRepository.findOne({
      where: {
        token: invitationId,
      },
      relations: {
        createdByAdmin: true,
        createdByConsultantAccount: {
          consultant: true,
        },
        createdByClientAccount: {
          company: true,
        },
        createdConsultantAccount: {
          user: true,
        },
        createdClientAccount: {
          user: true,
        },
        consultant: true,
        company: true,
      },
    });

    return await this.validate(invitation);
  }

  private async validate(
    invitation: Invitation | null,
  ): Promise<ValidationResult> {
    if (!invitation) {
      return {
        isValid: false,
        reason: InvitationInvalidReason.InvitationNotFound,
        invitation: null,
        existingUser: null,
      };
    }

    const existingUser = await this.userService.findByEmail(invitation.email);

    if (invitation.status === InvitationStatus.Invalid) {
      return {
        isValid: false,
        reason: InvitationInvalidReason.InvitationInvalid,
        invitation,
        existingUser,
      };
    }

    if (invitation.status === InvitationStatus.Active) {
      return {
        isValid: false,
        reason: InvitationInvalidReason.UserExists,
        invitation,
        existingUser,
      };
    }

    if (invitation.status === InvitationStatus.Expired) {
      return {
        isValid: false,
        reason: InvitationInvalidReason.InvitationExpired,
        invitation,
        existingUser,
      };
    }

    return {
      isValid: true,
      reason: null,
      invitation,
      existingUser,
    };
  }

  private async invitationOrUserExists(
    createInvitationDto: CreateInvitationDto,
  ): Promise<{ exists: boolean; reason: string }> {
    const { consultantId, companyId } = createInvitationDto;

    if (consultantId) {
      const existingInvitation = await this.invitationRepository.findOne({
        where: {
          email: createInvitationDto.email,
          consultant: { id: consultantId },
        },
        relations: {
          createdConsultantAccount: true,
          createdClientAccount: true,
        },
      });

      return {
        exists: !!existingInvitation,
        reason: existingInvitation?.createdConsultantAccount
          ? "A user with this email already has an account with this consultant."
          : "A user with this email has already been invited to this consultant.",
      };
    }

    const existingInvitationForOrganization =
      await this.invitationRepository.findOne({
        where: {
          email: createInvitationDto.email,
          company: { id: companyId },
        },
        relations: {
          createdConsultantAccount: true,
          createdClientAccount: true,
        },
      });

    return {
      exists: !!existingInvitationForOrganization,
      reason: existingInvitationForOrganization?.createdClientAccount
        ? "A user with this email already has an account with this company."
        : "A user with this email has already been invited to this company.",
    };
  }

  async canCreate(
    currentUser: RequestUser,
    invitationDto: { companyId?: string; consultantId?: string },
  ): Promise<boolean> {
    const { consultantId, companyId } = invitationDto;

    let viewerCanCreate = false;

    switch (currentUser.role) {
      case Role.Admin:
        // admin can only invite consultants
        if (consultantId) {
          viewerCanCreate = true;
        } else {
          viewerCanCreate = false;
        }
        break;
      case Role.Consultant:
        // consultant can invite other consultants to their own organization
        // but also invite clients to their child companies
        if (consultantId) {
          viewerCanCreate =
            consultantId === currentUser.consultantAccount.consultant.id;
        } else {
          const company = await this.companyService.findOrFail(companyId!, {
            relations: { consultant: true },
          });

          viewerCanCreate =
            company.consultant.id ===
            currentUser.consultantAccount.consultant.id;
        }
        break;
      case Role.Client:
        // clients can only invite other clients to their company
        if (consultantId) {
          viewerCanCreate = false;
        } else {
          viewerCanCreate = companyId === currentUser.clientAccount.company.id;
        }
        break;
    }

    return viewerCanCreate;
  }
}
