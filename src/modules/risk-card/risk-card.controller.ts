import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON><PERSON><PERSON>,
  Post,
} from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { CreateRiskCardDto } from "./dtos/create-risk-card.dto";
import { RiskCardResponseDto } from "./dtos/risk-card-response.dto";
import { RiskCard } from "./risk-card.entity";
import { RiskCardService } from "./risk-card.service";
import { RiskAssessmentService } from "../risk-assessment/risk-assessment.service";

@Controller("risk-card")
export class RiskCardController {
  constructor(
    private readonly riskCardService: RiskCardService,
    private readonly riskAssessmentService: RiskAssessmentService,
  ) {}

  // TODO not used by frontend
  @Post()
  @Roles(Role.Admin)
  async create(
    @Body() createRiskCardDto: CreateRiskCardDto,
  ): Promise<RiskCard> {
    return await this.riskCardService.create(createRiskCardDto);
  }

  @Get("/assessment/:id")
  @Roles(Role.Client, Role.Consultant)
  async findByRiskAssessment(
    @Param("id", new ParseUUIDPipe({ version: "4" })) riskAssessmentId: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ) {
    await this.riskAssessmentService.canAccess(riskAssessmentId, user);

    const riskGroups = await this.riskAssessmentService.findGroups(
      riskAssessmentId,
      {
        relations: {
          riskGroups: {
            riskCards: {
              owner: true,
              riskResponseActions: true,
            },
          },
        },
      },
    );

    const riskCards = riskGroups.flatMap((group) =>
      group.riskCards.map((card) => ({
        ...card,
        riskGroup: group,
      })),
    );

    return plainToInstance(
      RiskCardResponseDto,
      riskCards.map((card) => ({
        ...card,
      })),
      { excludeExtraneousValues: true },
    );
  }

  @Get(":id")
  @Roles(Role.Client, Role.Consultant)
  async find(
    @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ): Promise<RiskCardResponseDto> {
    const riskCard = await this.riskCardService.findOrFail(id);

    await this.riskAssessmentService.canAccess(
      riskCard.riskGroup.riskAssessmentId,
      user,
    );

    const relatedRiskCards =
      await this.riskCardService.findRelatedRiskCards(id);

    return plainToInstance(
      RiskCardResponseDto,
      { ...riskCard, relatedRiskCards },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  // @Patch(":id")
  // async update(
  //   @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  //   @Body() updateRiskCardDto: UpdateRiskCardDto,
  // ): Promise<RiskCard> {
  //   return await this.riskCardService.update(id, updateRiskCardDto);
  // }

  //This is an endpoint for only testing purposes
  @Post("/default")
  async createDefaultCards(
    @Body() createRiskGroupDto: { riskAssessmentId: string },
  ): Promise<RiskCard[]> {
    return await this.riskCardService.createRiskDefaultCards(
      createRiskGroupDto.riskAssessmentId,
    );
  }
}
