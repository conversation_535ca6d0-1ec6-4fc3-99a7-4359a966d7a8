import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
} from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { Company } from "./company.entity";
import { CompanyService } from "./company.service";
import { CompaniesResponseDto } from "./dtos/companies-response.dto";
import { CompanyResponseDto } from "./dtos/company-response.dto";
import { CreateCompanyDto } from "./dtos/create-company.dto";

@Controller("company")
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  // @Get()
  // @Roles(Role.Admin)
  // async findAll(): Promise<CompaniesResponseDto> {
  //   const companies = await this.companyService.findAll();

  //   return plainToInstance(
  //     CompaniesResponseDto,
  //     { companies },
  //     { excludeExtraneousValues: true },
  //   );
  // }

  @Get("by-consultant")
  @Roles(Role.Consultant)
  async findByConsultant(
    @CurrentUser() user: RequestUser<Role.Consultant>,
  ): Promise<CompaniesResponseDto> {
    const companies = await this.companyService.findByConsultant(user);

    return plainToInstance(
      CompaniesResponseDto,
      { companies },
      { excludeExtraneousValues: true },
    );
  }

  @Get("/:id")
  @Roles(Role.Admin, Role.Consultant)
  async findOne(
    @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
    @CurrentUser() user: RequestUser<Role.Admin | Role.Consultant>,
  ): Promise<CompanyResponseDto> {
    await this.companyService.canAccess(id, user);

    const company = await this.companyService.findOrFail(id, {
      relations: { riskAssessments: true, invitations: true },
      order: {
        riskAssessments: { createdAt: "ASC" },
        invitations: { createdAt: "ASC" },
      },
    });

    return plainToInstance(CompanyResponseDto, company, {
      excludeExtraneousValues: true,
    });
  }

  @Post()
  @Roles(Role.Consultant)
  async create(
    @Body() createCompanyDto: CreateCompanyDto,
    @CurrentUser() currentUser: RequestUser<Role.Consultant>,
  ): Promise<Company> {
    return await this.companyService.create(createCompanyDto, currentUser);
  }
}
