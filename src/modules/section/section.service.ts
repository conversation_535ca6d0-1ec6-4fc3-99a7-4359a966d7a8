import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { EntityManager, Repository } from "typeorm";

import { PartialSectionDto } from "./dtos/partial-section.dto";
import { Section } from "./section.entity";

@Injectable()
export class SectionService {
  constructor(
    @InjectRepository(Section)
    private sectionRepository: Repository<Section>,
  ) {}

  async create(
    dto: PartialSectionDto,
    parent?: Section,
    manager?: EntityManager,
  ): Promise<Section> {
    const { children, ...sectionData } = dto;

    const repo = manager
      ? manager.getRepository(Section)
      : this.sectionRepository;

    const section = repo.create({
      ...sectionData,
      parent,
    });

    const savedSection = await repo.save(section);

    if (children?.length) {
      savedSection.children = await Promise.all(
        children.map((childDto) =>
          this.create(childDto, savedSection, manager),
        ),
      );
    }

    return savedSection;
  }

  async createMany(sectionsToCreate: PartialSectionDto[]): Promise<Section[]> {
    const createdSections: Section[] = [];

    await this.sectionRepository.manager.transaction(async (manager) => {
      for (const sectionDto of sectionsToCreate) {
        const createdSection = await this.create(
          sectionDto,
          undefined,
          manager,
        );
        createdSections.push(createdSection);
      }
    });

    return createdSections;
  }

  async update(
    id: string,
    updateSectionDto: PartialSectionDto,
  ): Promise<Section> {
    const section = await this.sectionRepository.findOne({
      where: { id },
    });

    if (!section) {
      throw new NotFoundException(`Section with id: ${id} not found.`);
    }

    Object.assign(section, updateSectionDto);

    return await this.sectionRepository.save(section);
  }

  async findByContextReportId(
    contextReportId: string,
    parentId: string,
  ): Promise<Section[]> {
    return await this.sectionRepository.find({
      where: { contextReportId: contextReportId, parent: { id: parentId } },
      order: { position: "ASC" },
      relations: ["parent"],
    });
  }

  async findByContextReportIdWithoutParent(
    contextReportId: string,
  ): Promise<Section[]> {
    const rootSections = await this.sectionRepository
      .createQueryBuilder("section")
      .where("section.context_report_id = :contextReportId", {
        contextReportId,
      })
      .andWhere("section.parent_id IS NULL")
      .orderBy("section.position", "ASC")
      .getMany();

    return rootSections;
  }
}
