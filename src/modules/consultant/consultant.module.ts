import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { ConsultantController } from "./consultant.controller";
import { Consultant } from "./consultant.entity";
import { ConsultantService } from "./consultant.service";

@Module({
  imports: [TypeOrmModule.forFeature([Consultant])],
  providers: [ConsultantService],
  controllers: [ConsultantController],
  exports: [ConsultantService],
})
export class ConsultantModule {}
