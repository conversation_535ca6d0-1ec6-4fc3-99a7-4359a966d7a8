import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { RiskResponseType } from "@risk-card/risk-response-type.enum";

import { CreateRiskResponseActionDto } from "./dto/create-risk-response-action.dto";
import { RiskResponseAction } from "./risk-response-action.entity";

@Injectable()
export class RiskResponseActionService {
  constructor(
    @InjectRepository(RiskResponseAction)
    private riskResponseActionRepository: Repository<RiskResponseAction>,
  ) {}

  async create(
    createRiskResponseActionDto: CreateRiskResponseActionDto,
  ): Promise<RiskResponseAction> {
    return await this.riskResponseActionRepository.save(
      this.riskResponseActionRepository.create({
        ...createRiskResponseActionDto,
        riskCard: { id: createRiskResponseActionDto.riskCardId },
      }),
    );
  }

  async findByRiskAssessmentByMitigate(
    riskAssessmentId: string,
  ): Promise<RiskResponseAction[]> {
    return this.riskResponseActionRepository
      .createQueryBuilder("risk_response_action")
      .innerJoinAndSelect("risk_response_action.riskCard", "risk_card")
      .innerJoin("risk_card.riskGroup", "risk_group")
      .innerJoin("risk_group.riskAssessment", "risk_assessment")
      .where("risk_assessment.id = :riskAssessmentId", { riskAssessmentId })
      .andWhere("risk_card.risk_response_type = :mitigate", {
        mitigate: RiskResponseType.Mitigate,
      })
      .getMany();
  }
}
