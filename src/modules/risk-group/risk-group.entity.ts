import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";

import { RiskAssessment } from "../risk-assessment/risk-assessment.entity";
import { RiskCard } from "../risk-card/risk-card.entity";

@Entity()
export class RiskGroup extends BaseEntity {
  @Column()
  name: string;

  @ManyToOne(
    () => RiskAssessment,
    (riskAssessment) => riskAssessment.riskGroups,
  )
  @JoinColumn({ name: "risk_assessment_id" })
  riskAssessment: RiskAssessment;

  @Column({ name: "risk_assessment_id", type: "uuid" })
  riskAssessmentId: string;

  @OneToMany(() => RiskCard, (riskCard) => riskCard.riskGroup)
  riskCards: RiskCard[];
}
