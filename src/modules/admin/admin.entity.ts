import { Exclude } from "class-transformer";
import { IsEmail } from "class-validator";
import { Column, Entity, Index, OneToMany } from "typeorm";

import { Invitation } from "src/modules/invitation/invitation.entity";
import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

@Entity()
export class Admin extends BaseEntity {
  @Column({ unique: true })
  @IsEmail()
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ name: "first_name" })
  firstName: string;

  @Column({ name: "last_name" })
  lastName: string;

  @Column({ name: "failed_login_attempts", default: 0 })
  failedLoginAttempts: number;

  @Column({
    type: "timestamptz",
    name: "last_failed_login_attempt",
    nullable: true,
  })
  lastFailedLoginAttempt: Date | null;

  @Index()
  @NullableStringColumn({ name: "reset_password_token" })
  @Exclude()
  resetPasswordToken: string | null;

  @Column({
    type: "timestamptz",
    name: "reset_password_token_created_at",
    nullable: true,
  })
  @Exclude()
  resetPasswordTokenCreatedAt: Date | null;

  @OneToMany(() => Invitation, (invitation) => invitation.createdByAdmin)
  createdInvitations: Invitation[];
}
