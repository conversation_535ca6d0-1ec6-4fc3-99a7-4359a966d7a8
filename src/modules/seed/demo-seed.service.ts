import { Injectable, Logger } from "@nestjs/common";

import { CompanyService } from "@company/company.service";

import { RiskGroupService } from "@risk-group/risk-group.service";

import { RiskCardService } from "@risk-card/risk-card.service";

import { demoRiskAssessmentData } from "./demo-risk-assessment-data";
import { RiskResponseActionService } from "../risk-response-action/risk-response-action.service";

@Injectable()
export class DemoSeedSerivice {
  private readonly logger = new Logger(DemoSeedSerivice.name);

  constructor(
    private companyService: CompanyService,
    private riskGroupService: RiskGroupService,
    private riskCardService: RiskCardService,
    private riskResponseActionService: RiskResponseActionService,
  ) {}

  async run() {
    this.logger.log("Starting demo seeding...");

    const company = await this.companyService.findByName("VitalisRx");

    let ownerIndex = 0;

    for (const group of demoRiskAssessmentData) {
      const riskGroup = await this.riskGroupService.create({
        name: group.riskGroup,
        riskAssessmentId: company.riskAssessments[0].id,
      });

      if (group.riskCards.length > 0) {
        for (const card of group.riskCards) {
          const riskCard = await this.riskCardService.create({
            ...card,
            riskGroupId: riskGroup.id,
            ownerId:
              company.clientAccounts[ownerIndex % company.clientAccounts.length]
                .id,
          });

          if (card.riskResponseActions.length > 0) {
            for (const action of card.riskResponseActions) {
              await this.riskResponseActionService.create({
                ...action,
                riskCardId: riskCard.id,
              });
            }
          }

          ownerIndex++;
        }
      }
    }

    this.logger.log("Demo seeding completed");
  }
}
