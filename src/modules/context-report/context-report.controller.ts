import { <PERSON>, Get, Param } from "@nestjs/common";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { ContextReport } from "./context-report.entity";
import { ContextReportService } from "./context-report.service";
import { RiskAssessmentService } from "../risk-assessment/risk-assessment.service";

@Controller("context-report")
export class ContextReportController {
  constructor(
    private readonly contextReportService: ContextReportService,
    private readonly riskAssessmentService: RiskAssessmentService,
  ) {}

  @Get(":contextReportId")
  @Roles(Role.Client, Role.Consultant)
  async getContextReportWithSectionsById(
    @Param("contextReportId") contextReportId: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ): Promise<ContextReport | null> {
    const contextReport =
      await this.contextReportService.getByIdWithSectionTree(contextReportId);

    await this.riskAssessmentService.canAccess(
      contextReport.riskAssessment.id,
      user,
    );

    return contextReport;
  }
}
