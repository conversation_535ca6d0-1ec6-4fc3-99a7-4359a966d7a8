import { Exclude } from "class-transformer";
import { IsEmail } from "class-validator";
import { Entity, Column, OneToMany, Index } from "typeorm";

import { BaseEntity } from "@utils/base.entity";
import { NullableStringColumn } from "@utils/decorators/nullable-string.decorator";

import { ConsultantAccount } from "@consultant-account/consultant-account.entity";

import { ClientAccount } from "@client-account/client-account.entity";

@Entity("users")
export class User extends BaseEntity {
  @Column({ unique: true })
  @IsEmail()
  @Index()
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ name: "failed_login_attempts", default: 0 })
  failedLoginAttempts: number;

  @Column({
    type: "timestamptz",
    name: "last_failed_login_attempt",
    nullable: true,
  })
  lastFailedLoginAttempt: Date | null;

  @Index()
  @NullableStringColumn({ name: "reset_password_token" })
  @Exclude()
  resetPasswordToken: string | null;

  @Column({
    type: "timestamptz",
    name: "reset_password_token_created_at",
    nullable: true,
  })
  @Exclude()
  resetPasswordTokenCreatedAt: Date | null;

  @OneToMany(
    () => ConsultantAccount,
    (consultantAccount) => consultantAccount.user,
  )
  consultantAccounts: ConsultantAccount[];

  @OneToMany(() => ClientAccount, (clientAccount) => clientAccount.user)
  clientAccounts: ClientAccount[];
}
