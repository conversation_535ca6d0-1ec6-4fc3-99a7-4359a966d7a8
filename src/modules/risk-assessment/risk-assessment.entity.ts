import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { RiskAssessmentTemplate } from "src/utils/risk-assessment-template.enum";

import { Company } from "../company/company.entity";
import { Consultant } from "../consultant/consultant.entity";
import { ContextReport } from "../context-report/context-report.entity";
import { RiskGroup } from "../risk-group/risk-group.entity";

@Entity()
export class RiskAssessment extends BaseEntity {
  @Column()
  title: string;

  @Column({
    type: "enum",
    enum: RiskAssessmentTemplate,
  })
  template: RiskAssessmentTemplate;

  @OneToOne(
    () => ContextReport,
    (contextReport) => contextReport.riskAssessment,
  )
  @JoinColumn({ name: "context_report_id" })
  contextReport: ContextReport;

  @Column({ name: "context_report_id", type: "uuid" })
  contextReportId: string;

  @OneToMany(() => RiskGroup, (riskGroup) => riskGroup.riskAssessment, {
    cascade: true,
  })
  riskGroups: RiskGroup[];

  @ManyToOne(() => Company, (company) => company.riskAssessments, {
    nullable: false,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "company_id" })
  company: Company;

  @ManyToOne(() => Consultant, (consultant) => consultant.riskAssessments, {
    nullable: false,
  })
  @JoinColumn({ name: "consultant_id" })
  consultant: Consultant;
}
