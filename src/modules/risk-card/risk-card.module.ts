import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { ClientAccountModule } from "@client-account/client-account.module";

import { RiskCardController } from "./risk-card.controller";
import { RiskCard } from "./risk-card.entity";
import { RiskCardService } from "./risk-card.service";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";
import { RiskGroupModule } from "../risk-group/risk-group.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([RiskCard]),
    RiskGroupModule,
    RiskAssessmentModule,
    RiskDomainModule,
    ClientAccountModule,
  ],
  providers: [RiskCardService],

  controllers: [RiskCardController],
  exports: [RiskCardService],
})
export class RiskCardModule {}
