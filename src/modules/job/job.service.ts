import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { plainToInstance } from "class-transformer";
import { validateOrReject } from "class-validator";
import { FindOptionsRelations, Repository } from "typeorm";

import { RequestUser } from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { JobStatus } from "./enum/job-status.enum";
import { ContextReportService } from "../context-report/context-report.service";
import { JobInputDto } from "./dto/job-input.dto";
import { UpdateJobDto } from "./dto/update-job.dto";
import { JobType } from "./enum/job-type.enum";
import { Job } from "./job.entity";
import { RiskAssessmentService } from "../risk-assessment/risk-assessment.service";

@Injectable()
export class JobService {
  private readonly logger = new Logger(ContextReportService.name);

  constructor(
    @InjectRepository(Job)
    private jobRepository: Repository<Job>,
    private readonly contextReportService: ContextReportService,
    private readonly riskAssessmentService: RiskAssessmentService,
  ) {}

  async create(createJobDto: JobInputDto) {
    try {
      let jobToBeCreated = null;
      if (createJobDto.type === JobType.GenerateContextReport) {
        const contextReport = await this.contextReportService.findOne(
          createJobDto.id,
        );

        if (!contextReport) {
          throw new NotFoundException(
            `Context report with ID ${createJobDto.id} not found`,
          );
        }

        jobToBeCreated = {
          input: { type: createJobDto.type, id: contextReport.id },
          company: contextReport.riskAssessment.company,
        };
      } else if (createJobDto.type === JobType.GenerateRiskAssessment) {
        const riskAssessment = await this.riskAssessmentService.find(
          createJobDto.id,
          { relations: { company: true } },
        );

        if (!riskAssessment) {
          throw new NotFoundException(
            `Risk assessment with ID ${createJobDto.id} not found`,
          );
        }

        jobToBeCreated = {
          input: { type: createJobDto.type, id: riskAssessment.id },
          company: riskAssessment.company,
        };
      }

      if (jobToBeCreated) {
        const job = await this.jobRepository.create(jobToBeCreated);
        const savedJob = await this.jobRepository.save(job);
        this.logger.log("The job has been successfully created.", savedJob.id);
        return savedJob;
      }
    } catch (error) {
      this.logger.error("Error in creating job", error);
      throw error;
    }
  }

  async findOneOrFail(
    id: string,
    { relations }: { relations?: FindOptionsRelations<Job> } = {},
  ) {
    const job = await this.jobRepository.findOne({
      where: { id },
      relations,
    });

    if (!job) {
      throw new NotFoundException(`Job with id: ${id} not found.`);
    }

    return job;
  }

  async update(id: string, updateJobDto: UpdateJobDto): Promise<Job> {
    const job = await this.jobRepository.findOne({
      where: { id },
    });

    if (!job) {
      throw new NotFoundException(`Job with id: ${id} not found.`);
    }

    Object.assign(job, updateJobDto);

    return await this.jobRepository.save(job);
  }

  async getByStatus(status: JobStatus): Promise<Job | null> {
    return this.jobRepository.findOne({
      where: { status },
      relations: ["company"],
    });
  }

  async unpackJobInput(input: Record<string, unknown>): Promise<JobInputDto> {
    const dto = plainToInstance(JobInputDto, input);
    await validateOrReject(dto);
    return dto;
  }

  async canAccess(user: RequestUser<Role.Consultant>, jobId: string) {
    const job = await this.findOneOrFail(jobId, {
      relations: { company: { consultant: true } },
    });

    if (job.company.consultant.id !== user.consultantAccount.consultant.id) {
      throw new ForbiddenException();
    }
  }

  async canCreate(
    user: RequestUser<Role.Consultant>,
    jobInputDto: JobInputDto,
  ) {
    if (jobInputDto.type === JobType.GenerateContextReport) {
      const contextReport = await this.contextReportService.findOneOrFail(
        jobInputDto.id,
      );

      if (
        contextReport.riskAssessment.consultant.id !==
        user.consultantAccount.consultant.id
      ) {
        throw new ForbiddenException();
      }
    } else if (jobInputDto.type === JobType.GenerateRiskAssessment) {
      const riskAssessment = await this.riskAssessmentService.findOrFail(
        jobInputDto.id,
        { relations: { consultant: true } },
      );

      if (
        riskAssessment.consultant.id !== user.consultantAccount.consultant.id
      ) {
        throw new ForbiddenException();
      }
    }
  }

  async canManage(user: RequestUser<Role.Consultant>, jobId: string) {
    const job = await this.findOneOrFail(jobId, {
      relations: { company: { consultant: true } },
    });

    if (job.input.type === JobType.GenerateContextReport) {
      const contextReport = await this.contextReportService.findOneOrFail(
        job.input.id,
      );

      if (
        contextReport.riskAssessment.consultant.id !==
        user.consultantAccount.consultant.id
      ) {
        throw new ForbiddenException();
      }
    } else if (job.input.type === JobType.GenerateRiskAssessment) {
      const riskAssessment = await this.riskAssessmentService.findOrFail(
        job.input.id,
        { relations: { consultant: true } },
      );

      if (
        riskAssessment.consultant.id !== user.consultantAccount.consultant.id
      ) {
        throw new ForbiddenException();
      }
    }

    return job;
  }

  async isCanceled(jobId: string) {
    const job = await this.findOneOrFail(jobId);

    if (job.status === JobStatus.CancelingInProgress) {
      await this.update(jobId, {
        status: JobStatus.Canceled,
      });
      return true;
    }

    return false;
  }

  async cancelJob(jobId: string) {
    const job = await this.findOneOrFail(jobId);

    job.status =
      job.status === JobStatus.InProgress
        ? JobStatus.CancelingInProgress
        : JobStatus.Canceled;

    return await this.jobRepository.save(job);
  }
}
