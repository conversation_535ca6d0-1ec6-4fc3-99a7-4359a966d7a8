import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";

import { RiskCard } from "@risk-card/risk-card.entity";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { Company } from "../company/company.entity";
import { Invitation } from "../invitation/invitation.entity";
import { Permission } from "../permission/permission.entity";
import { User } from "../user/user.entity";

@Entity()
export class ClientAccount extends BaseEntity {
  @Column()
  name: string;

  @NullableStringColumn()
  avatar: string | null;

  @NullableStringColumn({ name: "job_title" })
  jobTitle: string | null;

  @ManyToOne(() => User, (user) => user.clientAccounts, {
    nullable: false,
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => Company, (company) => company.clientAccounts, {
    nullable: false,
  })
  @JoinColumn({ name: "company_id" })
  company: Company;

  @OneToMany(
    () => Invitation,
    (invitation) => invitation.createdByClientAccount,
  )
  createdInvitations: Invitation[];

  @OneToOne(() => Invitation, (invitation) => invitation.createdClientAccount)
  invitation: Invitation;

  @ManyToMany(() => Permission, (permission) => permission.clientAccounts)
  @JoinTable({ name: "client_account_permission" })
  permissions: Permission[];

  @OneToMany(() => RiskCard, (riskCard) => riskCard.owner)
  ownedRiskCards: RiskCard[];
}
