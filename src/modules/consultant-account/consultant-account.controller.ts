import { Body, Controller, Param, Post } from "@nestjs/common";

import { ParseNanoIDPipe } from "src/utils/nanoid";

import { ConsultantAccount } from "./consultant-account.entity";
import { ConsultantAccountService } from "./consultant-account.service";
import { RegisterFromInvitationDto } from "./register-from-invitation.dto";
import { Public } from "../auth/decorators/public.decorator";

@Controller("consultant-account")
export class ConsultantAccountController {
  constructor(
    private readonly consultantAccountService: ConsultantAccountService,
  ) {}

  @Public()
  @Post("from-invitation/:token")
  async registerFromInvitation(
    @Param("token", ParseNanoIDPipe) token: string,
    @Body() registerFromInvitationDto: RegisterFromInvitationDto,
  ): Promise<ConsultantAccount> {
    return await this.consultantAccountService.registerFromInvitation(
      token,
      registerFromInvitationDto,
    );
  }

  @Public()
  @Post("accept-invitation/:token")
  async acceptInvitation(
    @Param("token", ParseNanoIDPipe) token: string,
  ): Promise<ConsultantAccount> {
    return await this.consultantAccountService.acceptInvitation(token);
  }
}
