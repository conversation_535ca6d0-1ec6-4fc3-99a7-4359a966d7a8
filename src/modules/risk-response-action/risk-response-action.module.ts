import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { RiskResponseActionController } from "./risk-response-action.controller";
import { RiskResponseAction } from "./risk-response-action.entity";
import { RiskResponseActionService } from "./risk-response-action.service";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([RiskResponseAction]),
    RiskAssessmentModule,
    RiskDomainModule,
  ],
  providers: [RiskResponseActionService],

  controllers: [RiskResponseActionController],
  exports: [RiskResponseActionService],
})
export class RiskResponseActionModule {}
