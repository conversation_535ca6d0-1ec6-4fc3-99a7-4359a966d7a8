import { Controller, Get } from "@nestjs/common";

import { User } from "./user.entity";
import { UserService } from "./user.service";

@Controller("user")
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async findAll(): Promise<User[]> {
    return await this.userService.findAll();
  }

  // @Get(":id")
  // async find(
  //   @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  // ): Promise<User> {
  //   return await this.userService.findOrFail(id);
  // }

  // @Patch(":id")
  // async update(
  //   @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  //   @Body() updateUserDto: UpdateUserDto,
  // ): Promise<User> {
  //   return await this.userService.update(id, updateUserDto);
  // }

  // @Delete(":id")
  // @HttpCode(204)
  // async delete(
  //   @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  // ): Promise<void> {
  //   await this.userService.delete(id);
  //   return;
  // }
}
