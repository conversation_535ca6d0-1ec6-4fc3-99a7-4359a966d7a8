import { Link, Preview } from "@react-email/components";
import * as React from "react";

import "dotenv/config";

import { Button } from "./components/Button";
import { EmailContainer } from "./components/EmailContainer";
import { Text } from "./components/Text";

interface ResetPasswordEmailProps {
  token: string;
  isAdmin: boolean;
}

export const ResetPasswordEmail = ({
  token,
  isAdmin,
}: ResetPasswordEmailProps) => (
  <EmailContainer>
    <Preview>Reset your password on Eastward</Preview>

    <Text size="lg" weight="semibold" className="text-center mt-4 mx-8">
      Reset your password!
    </Text>

    <Text size="sm" className="text-center mt-4 mx-8">
      You recently requested a password reset for your{" "}
      <Text as="span" size="sm" color="accent-700" weight="semibold">
        Eastward account
      </Text>
      . To complete the process, click the link below.
    </Text>

    <Text size="sm" className="text-center mt-4 mx-8">
      After you click on the link you will be redirected to the Eeastward
      platform where you will be able to complete your password reset.
    </Text>

    <Button
      text="Reset now"
      className="mt-8"
      href={
        isAdmin
          ? `${process.env.WEB_URL}/admin/reset-password/${token}`
          : `${process.env.WEB_URL}/reset-password/${token}`
      }
    />

    <Text size="sm" className="text-center mt-4">
      If you didn’t make this change or if you believe an unauthorized person
      has accessed your account email us at{" "}
      <Link href="mailto:<EMAIL>">
        <Text as="span" size="sm" color="accent-700" weight="semibold" isLink>
          <EMAIL>
        </Text>
      </Link>
    </Text>
  </EmailContainer>
);

ResetPasswordEmail.PreviewProps = {
  token: "not_a_valid_token",
  isAdmin: false,
} satisfies ResetPasswordEmailProps;

export default ResetPasswordEmail;
