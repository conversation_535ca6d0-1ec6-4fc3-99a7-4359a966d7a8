{"compilerOptions": {"module": "commonjs", "declaration": true, "emitDeclarationOnly": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@utils/*": ["src/utils/*"], "@db/*": ["src/db/*"], "@config/*": ["src/config/*"], "@emails/*": ["src/emails/*"], "@user/*": ["src/modules/user/*"], "@admin/*": ["src/modules/admin/*"], "@auth/*": ["src/modules/auth/*"], "@consultant-account/*": ["src/modules/consultant-account/*"], "@client-account/*": ["src/modules/client-account/*"], "@company/*": ["src/modules/company/*"], "@consultant/*": ["src/modules/consultant/*"], "@invitation/*": ["src/modules/invitation/*"], "@permission/*": ["src/modules/permission/*"], "@context-report/*": ["src/modules/context-report/*"], "@risk-assessment/*": ["src/modules/risk-assessment/*"], "@risk-domain/*": ["src/modules/risk-domain/*"], "@risk-group/*": ["src/modules/risk-group/*"], "@risk-card/*": ["src/modules/risk-card/*"], "@section/*": ["src/modules/section/*"], "@document/*": ["src/modules/document/*"], "@job/*": ["src/modules/job/*"], "@ai/*": ["src/modules/ai/*"], "@seed/*": ["src/modules/seed/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "strict": true, "strictPropertyInitialization": false, "jsx": "react"}}