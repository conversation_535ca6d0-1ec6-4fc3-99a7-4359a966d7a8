import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { RequestUser } from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { CreateDocumentDto } from "./create-document.dto";
import { Document } from "./document.entity";
import { CompanyService } from "../company/company.service";
import { MulterFile, S3Service } from "../s3/s3.service";

@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  constructor(
    @InjectRepository(Document)
    private userRepository: Repository<Document>,
    private s3Service: S3Service,
    private companyService: CompanyService,
  ) {}

  async findByS3Url(s3Url: string): Promise<Document | null> {
    return await this.userRepository.findOne({
      where: {
        s3Url,
      },
    });
  }

  async create(
    file: MulterFile,
    createDocumentDto: CreateDocumentDto,
  ): Promise<Document> {
    const url = await this.s3Service.uploadFile(
      file,
      createDocumentDto.companyId,
    );

    if (!url) {
      throw new Error("Failed to upload file to S3");
    }

    //TODO: Change this if we decide, in s3, not to rewrite files with the same name. For this, we need to implement deleting files (waiting for product/design)
    const existingDocument = await this.findByS3Url(url);

    if (existingDocument) {
      return existingDocument;
    }

    const newDocument = this.userRepository.create({
      name: createDocumentDto.name,
      s3Url: url,
      company: { id: createDocumentDto.companyId },
    });

    return await this.userRepository.save(newDocument);
  }

  async uploadMultiple(
    files: MulterFile[],
    companyId: string,
  ): Promise<Document[]> {
    const results = [];

    for (const file of files) {
      const newDocument = await this.create(file, {
        name: file.originalname,
        companyId,
      });

      results.push(newDocument);
    }

    return results;
  }

  async canCreate(
    currentUser: RequestUser<Role.Consultant>,
    companyId: string,
  ) {
    const company = await this.companyService.findOrFail(companyId, {
      relations: { consultant: true },
    });

    if (company.consultant.id !== currentUser.consultantAccount.consultant.id) {
      throw new ForbiddenException();
    }
  }
}
