#!/bin/bash

# THIS SCRIPT IS MOSTLY AI GENERATED

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"

TEMPLATE_FILE="$SCRIPT_DIR/CreateAdmin-template.ts"
MIGRATIONS_DIR="$SCRIPT_DIR/migrations"

while getopts "f:l:p:e:" opt; do
  case $opt in
    f) FIRST_NAME="$OPTARG" ;;
    l) LAST_NAME="$OPTARG" ;;
    p) PASSWORD="$OPTARG" ;;
    e) EMAIL="$OPTARG" ;;
    *) echo "Invalid option"; exit 1 ;;
  esac
done

if [[ -z "$FIRST_NAME" || -z "$LAST_NAME" || -z "$PASSWORD" || -z "$EMAIL" ]]; then
  echo "Usage: $0 -f firstName -l lastName -p password -e email"
  exit 1
fi

# typeorm expects ts in milliseconds, but mac can't natively generate that
TIMESTAMP="$(date +%s)000"
NEW_FILE="$MIGRATIONS_DIR/${TIMESTAMP}-CreateAdmin.ts"

sed \
  -e "s/__EMAIL__/${EMAIL//\//\\/}/g" \
  -e "s/__PASSWORD__/${PASSWORD//\//\\/}/g" \
  -e "s/__FIRST_NAME__/${FIRST_NAME//\//\\/}/g" \
  -e "s/__LAST_NAME__/${LAST_NAME//\//\\/}/g" \
  -e "s/__TIMESTAMP__/${TIMESTAMP}/g" \
  "$TEMPLATE_FILE" > "$NEW_FILE"

echo "Created migration: $NEW_FILE"
