import { NestFactory } from "@nestjs/core";
import * as dotenv from "dotenv";

import { SeedModule } from "./seed.module";
import { SeedService } from "./seed.service";

// this must be loaded before SeedModule
dotenv.config();

async function bootstrap() {
  const appCtx = await NestFactory.createApplicationContext(SeedModule);

  const seedService = appCtx.get(SeedService);

  await seedService.seed();

  await seedService.demoSeed();

  await appCtx.close();
}

bootstrap();
