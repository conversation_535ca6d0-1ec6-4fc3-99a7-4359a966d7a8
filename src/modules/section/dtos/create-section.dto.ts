import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>nt,
  <PERSON><PERSON>ot<PERSON>mpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>idateNested,
} from "class-validator";

import { IsNotEmptyString } from "src/utils/decorators/is-not-empty-string.decorator";

import { PartialSectionDto } from "./partial-section.dto";

export class CreateSectionDto {
  @IsNotEmptyString()
  title: string;

  @IsInt()
  @IsNotEmpty()
  level: number;

  @IsInt()
  @IsNotEmpty()
  position: number;

  @IsNotEmptyString()
  prompt: string;

  @IsNotEmptyString()
  response: string;

  @IsNotEmptyString()
  text: string;

  @IsNotEmptyString()
  citedFrom: string;

  @IsUUID(4)
  contextReportId: string;

  @IsArray()
  @ValidateNested({ each: true })
  children?: PartialSectionDto[];
}
