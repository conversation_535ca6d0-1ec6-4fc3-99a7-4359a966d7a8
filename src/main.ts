import { Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import * as cookieParser from "cookie-parser";
import * as dotenv from "dotenv";

dotenv.config();

import { AppModule } from "./app.module";

async function bootstrap() {
  const logger = new Logger("Bootstrap");
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  app.enableCors({
    origin: ["https://eastwardqa.arsfutura.co", /http:\/\/localhost:\d+/],
    credentials: true,
  });
  app.use(cookieParser());

  const port = configService.get<number>("PORT") || 3000;
  await app.listen(port);
  logger.log(`Application is running on port ${port}`);
}

bootstrap().catch((error) => {
  throw new Error(`Error starting the application: ${error}`);
});
