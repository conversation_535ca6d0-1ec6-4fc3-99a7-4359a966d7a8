import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON> } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";

import { ClientAccount } from "../client-account/client-account.entity";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";

@Entity()
export class Permission extends BaseEntity {
  @Column()
  name: string;

  @Column()
  description: string;

  @ManyToMany(
    () => ConsultantAccount,
    (consultantAccount) => consultantAccount.permissions,
  )
  consultantAccounts: ConsultantAccount;

  @ManyToMany(() => ClientAccount, (clientAccount) => clientAccount.permissions)
  clientAccounts: ClientAccount;
}
