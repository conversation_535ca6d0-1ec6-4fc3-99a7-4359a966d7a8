import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { ConsultantAccountController } from "./consultant-account.controller";
import { ConsultantAccount } from "./consultant-account.entity";
import { ConsultantAccountService } from "./consultant-account.service";
import { InvitationModule } from "../invitation/invitation.module";
import { User } from "../user/user.entity";
import { UserModule } from "../user/user.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([ConsultantAccount, User]),
    forwardRef(() => InvitationModule),
    UserModule,
  ],
  providers: [ConsultantAccountService],
  controllers: [ConsultantAccountController],
  exports: [ConsultantAccountService],
})
export class ConsultantAccountModule {}
