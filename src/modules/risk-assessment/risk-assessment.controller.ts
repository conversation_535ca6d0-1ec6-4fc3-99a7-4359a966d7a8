import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON><PERSON><PERSON>,
  Post,
} from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { RiskAssessmentTemplate } from "src/utils/risk-assessment-template.enum";
import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { RiskAssessmentBasicInfoDto } from "./dtos/risk-assessment-basic-info.dto";
import { RiskAssessmentGroupsDto } from "./dtos/risk-assessment-groups.dto";
import { RiskAssessmentService } from "./risk-assessment.service";

@Controller("risk-assessment")
export class RiskAssessmentController {
  constructor(private readonly riskAssessmentService: RiskAssessmentService) {}

  @Get(":id/basic-info")
  @Roles(Role.Client, Role.Consultant)
  async find(
    @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ): Promise<RiskAssessmentBasicInfoDto> {
    await this.riskAssessmentService.canAccess(id, user);

    const riskAssessment = await this.riskAssessmentService.findOrFail(id, {
      relations: { contextReport: true, company: true },
    });

    return plainToInstance(RiskAssessmentBasicInfoDto, riskAssessment, {
      excludeExtraneousValues: true,
    });
  }

  @Get("/:id/groups")
  async findGroups(
    @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
    @CurrentUser() user: RequestUser<Role.Client | Role.Consultant>,
  ): Promise<RiskAssessmentGroupsDto> {
    await this.riskAssessmentService.canAccess(id, user);

    const groups = await this.riskAssessmentService.findGroups(id, {
      relations: { riskGroups: { riskCards: true } },
    });

    return plainToInstance(
      RiskAssessmentGroupsDto,
      { groups },
      { excludeExtraneousValues: true },
    );
  }

  //This is an endpoint for only testing purposes
  @Post()
  async create(
    @Body()
    createRiskAssessmentDto: {
      companyId: string;
      consultantId: string;
    },
  ) {
    const riskAssessment = await this.riskAssessmentService.create(
      {
        title: "2025 Risk Assessment Report",
        template: RiskAssessmentTemplate.Pharmacy,
        companyId: createRiskAssessmentDto.companyId,
      },
      {
        consultantAccount: {
          consultant: { id: createRiskAssessmentDto.consultantId },
        },
      } as any,
    );

    return riskAssessment;
  }
}
