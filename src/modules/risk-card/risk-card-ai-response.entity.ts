import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";
import { NullableStringColumn } from "src/utils/decorators/nullable-string.decorator";

import { RiskCard } from "./risk-card.entity";
import { RiskResponseType } from "./risk-response-type.enum";

@Entity()
export class RiskCardAiResponse extends BaseEntity {
  @NullableStringColumn({ name: "impact_summary" })
  impactSummary: string | null;

  @NullableStringColumn({ name: "financial_impact" })
  financialImpact: string | null;

  @NullableStringColumn({ name: "non_financial_impact" })
  nonFinancialImpact: string | null;

  @NullableStringColumn({ name: "impact_velocity" })
  impactVelocity: string | null;

  @NullableStringColumn({ name: "impact_persistence" })
  impactPersistence: string | null;

  @NullableStringColumn({ name: "likelihood_summary" })
  likelihoodSum<PERSON>y: string | null;

  @NullableStringColumn({ name: "quant_likelihood" })
  quantLikelihood: string | null;

  @NullableStringColumn({ name: "qualit_likelihood" })
  qualitLikelihood: string | null;

  @Column({ type: "numeric", default: 0, name: "impact_score" })
  impactScore: number;

  @Column({ type: "numeric", default: 0, name: "likelihood_score" })
  likelihoodScore: number;

  @Column({ type: "numeric", default: 0, name: "control_score" })
  controlScore: number;

  @NullableStringColumn({ name: "control_summary" })
  controlSummary: string | null;

  @NullableStringColumn({
    name: "risk_response_type",
    type: "enum",
    enum: RiskResponseType,
  })
  riskResponseType: RiskResponseType;

  @OneToOne(() => RiskCard, (riskCard) => riskCard.aiResponse)
  @JoinColumn({ name: "risk_card_id" })
  riskCard: RiskCard;

  @Column({ name: "risk_card_id", type: "uuid" })
  riskCardId: string;
}
