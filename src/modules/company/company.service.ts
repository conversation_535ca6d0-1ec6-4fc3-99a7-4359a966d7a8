import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { FindOptionsOrder, FindOptionsRelations, Repository } from "typeorm";

import {
  CurrentUser,
  RequestUser,
} from "src/utils/decorators/current-user.decorator";
import { Role } from "src/utils/roles/role";

import { Company } from "./company.entity";
import { CreateCompanyDto } from "./dtos/create-company.dto";

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async create(
    createCompanyDto: CreateCompanyDto,
    @CurrentUser() currentUser: RequestUser<Role.Consultant>,
  ): Promise<Company> {
    return await this.companyRepository.save(
      this.companyRepository.create({
        name: createCompanyDto.name,
        website: createCompanyDto.website,
        consultant: { id: currentUser.consultantAccount.consultant.id },
      }),
    );
  }

  async findByConsultant(
    user: RequestUser<Role.Consultant>,
  ): Promise<Company[]> {
    return await this.companyRepository.find({
      where: {
        consultant: { id: user.consultantAccount.consultant.id },
      },
      relations: { riskAssessments: true, clientAccounts: true },
      order: { createdAt: "DESC" },
    });
  }

  async findAll(): Promise<Company[]> {
    return await this.companyRepository.find({
      relations: { riskAssessments: true, clientAccounts: true },
    });
  }

  async find(
    id: string,
    {
      relations,
      order,
    }: {
      relations?: FindOptionsRelations<Company>;
      order?: FindOptionsOrder<Company>;
    } = {},
  ): Promise<Company | null> {
    return await this.companyRepository.findOne({
      where: { id },
      relations,
      order,
    });
  }

  async findOrFail(
    id: string,
    {
      relations,
      order,
    }: {
      relations?: FindOptionsRelations<Company>;
      order?: FindOptionsOrder<Company>;
    } = {},
  ): Promise<Company> {
    const company = await this.find(id, { relations, order });

    if (!company) {
      throw new NotFoundException("Company not found.");
    }

    return company;
  }

  async canAccess(id: string, user: RequestUser) {
    const company = await this.findOrFail(id, {
      relations: { consultant: true },
    });

    let viewerCanAccess = false;
    switch (user.role) {
      case Role.Admin:
        viewerCanAccess = true;
        break;
      case Role.Consultant:
        viewerCanAccess =
          company.consultant.id === user.consultantAccount.consultant.id;
        break;
      case Role.Client:
        viewerCanAccess = user.clientAccount.company.id === company.id;
        break;
    }

    if (!viewerCanAccess) {
      throw new ForbiddenException();
    }
  }

  /**
   * @deprecated Only for seeding purposes. Do not use this method for anything else.
   */
  async findByName(name: string): Promise<Company> {
    return await this.companyRepository.findOneOrFail({
      where: { name },
      relations: { riskAssessments: true, clientAccounts: true },
    });
  }
}
