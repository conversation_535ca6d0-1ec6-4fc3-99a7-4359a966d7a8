import { MigrationInterface, QueryRunner } from "typeorm";

export class SetUpUserManagment1751283604193 implements MigrationInterface {
  name = "SetUpUserManagment1751283604193";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "admin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "email" character varying NOT NULL, "password" character varying NOT NULL, "first_name" character varying NOT NULL, "last_name" character varying NOT NULL, CONSTRAINT "UQ_de87485f6489f5d0995f5841952" UNIQUE ("email"), CONSTRAINT "PK_e032310bcef831fb83101899b10" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "invitation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "token" character varying NOT NULL, "email" character varying NOT NULL, "name" character varying NOT NULL, "job_title" character varying NOT NULL, "expires_at" TIMESTAMP NOT NULL, "meta" jsonb, "consultant_id" uuid, "company_id" uuid, "created_by_consultant_account_id" uuid, "created_by_client_account_id" uuid, "created_by_admin_id" uuid, "created_consultant_account_id" uuid, "created_client_account_id" uuid, CONSTRAINT "REL_0c60dfee1f3d8e7fc3d96e859b" UNIQUE ("created_consultant_account_id"), CONSTRAINT "REL_a7f1d7580cde28b1ab24225bf9" UNIQUE ("created_client_account_id"), CONSTRAINT "PK_beb994737756c0f18a1c1f8669c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e061236e6abd8503aa3890af94" ON "invitation" ("token") `,
    );
    await queryRunner.query(
      `CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "description" character varying NOT NULL, CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "consultant_account" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "avatar" character varying, "job_title" character varying NOT NULL, "user_id" uuid NOT NULL, "consultant_id" uuid NOT NULL, CONSTRAINT "PK_8fd133bafacaa4c10b04bb50b72" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "consultant" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "logo_url" character varying, "website" character varying NOT NULL, CONSTRAINT "PK_fc6968da0e8b2cb9315222e4bc9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "company" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "logo_url" character varying, "website" character varying NOT NULL, "consultant_id" uuid NOT NULL, CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "client_account" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "name" character varying NOT NULL, "avatar" character varying, "job_title" character varying NOT NULL, "user_id" uuid NOT NULL, "company_id" uuid NOT NULL, CONSTRAINT "PK_b3627c981b3d782cb5a2845e3d8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "email" character varying NOT NULL, "password" character varying NOT NULL, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_97672ac88f789774dd47f7c8be" ON "users" ("email") `,
    );
    await queryRunner.query(
      `CREATE TABLE "consultant_account_permission" ("consultantAccountId" uuid NOT NULL, "permissionId" uuid NOT NULL, CONSTRAINT "PK_d30b97cd89c8c6b5a87c0a53430" PRIMARY KEY ("consultantAccountId", "permissionId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f76ab03ffbab5dec67001a181c" ON "consultant_account_permission" ("consultantAccountId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e121072810e1fd4821e00fd555" ON "consultant_account_permission" ("permissionId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "client_account_permission" ("clientAccountId" uuid NOT NULL, "permissionId" uuid NOT NULL, CONSTRAINT "PK_e545ec41c41579ee87093213b08" PRIMARY KEY ("clientAccountId", "permissionId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_860e08644b1bd1ad05e1723edf" ON "client_account_permission" ("clientAccountId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1235321cfa45ef1464cefb13b3" ON "client_account_permission" ("permissionId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_c5d32bcc0db86c1ef213a9af987" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_088c9b15c9465faaa40472b71cf" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_a4d0d067498c85c0bcad085411a" FOREIGN KEY ("created_by_consultant_account_id") REFERENCES "consultant_account"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_b532d9f1587436b9c37ee314113" FOREIGN KEY ("created_by_client_account_id") REFERENCES "client_account"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_947d75277dfde77b832620c5bcc" FOREIGN KEY ("created_by_admin_id") REFERENCES "admin"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_0c60dfee1f3d8e7fc3d96e859ba" FOREIGN KEY ("created_consultant_account_id") REFERENCES "consultant_account"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" ADD CONSTRAINT "FK_a7f1d7580cde28b1ab24225bf93" FOREIGN KEY ("created_client_account_id") REFERENCES "client_account"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" ADD CONSTRAINT "FK_a7fffc45f7af1fe09d4c245fe19" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" ADD CONSTRAINT "FK_0468736b36ade1870571db86e8c" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" ADD CONSTRAINT "FK_0bf9c2e12f5b9a71665bbc71c59" FOREIGN KEY ("consultant_id") REFERENCES "consultant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account" ADD CONSTRAINT "FK_4e5cbcfaba61efa27b4ab047570" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account" ADD CONSTRAINT "FK_c6f046dfb3ed1d9c92c4fb81d2e" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account_permission" ADD CONSTRAINT "FK_f76ab03ffbab5dec67001a181c5" FOREIGN KEY ("consultantAccountId") REFERENCES "consultant_account"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account_permission" ADD CONSTRAINT "FK_e121072810e1fd4821e00fd5555" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account_permission" ADD CONSTRAINT "FK_860e08644b1bd1ad05e1723edf4" FOREIGN KEY ("clientAccountId") REFERENCES "client_account"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account_permission" ADD CONSTRAINT "FK_1235321cfa45ef1464cefb13b3c" FOREIGN KEY ("permissionId") REFERENCES "permission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "client_account_permission" DROP CONSTRAINT "FK_1235321cfa45ef1464cefb13b3c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account_permission" DROP CONSTRAINT "FK_860e08644b1bd1ad05e1723edf4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account_permission" DROP CONSTRAINT "FK_e121072810e1fd4821e00fd5555"`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account_permission" DROP CONSTRAINT "FK_f76ab03ffbab5dec67001a181c5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account" DROP CONSTRAINT "FK_c6f046dfb3ed1d9c92c4fb81d2e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "client_account" DROP CONSTRAINT "FK_4e5cbcfaba61efa27b4ab047570"`,
    );
    await queryRunner.query(
      `ALTER TABLE "company" DROP CONSTRAINT "FK_0bf9c2e12f5b9a71665bbc71c59"`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" DROP CONSTRAINT "FK_0468736b36ade1870571db86e8c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "consultant_account" DROP CONSTRAINT "FK_a7fffc45f7af1fe09d4c245fe19"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_a7f1d7580cde28b1ab24225bf93"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_0c60dfee1f3d8e7fc3d96e859ba"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_947d75277dfde77b832620c5bcc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_b532d9f1587436b9c37ee314113"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_a4d0d067498c85c0bcad085411a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_088c9b15c9465faaa40472b71cf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invitation" DROP CONSTRAINT "FK_c5d32bcc0db86c1ef213a9af987"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1235321cfa45ef1464cefb13b3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_860e08644b1bd1ad05e1723edf"`,
    );
    await queryRunner.query(`DROP TABLE "client_account_permission"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e121072810e1fd4821e00fd555"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f76ab03ffbab5dec67001a181c"`,
    );
    await queryRunner.query(`DROP TABLE "consultant_account_permission"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_97672ac88f789774dd47f7c8be"`,
    );
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TABLE "client_account"`);
    await queryRunner.query(`DROP TABLE "company"`);
    await queryRunner.query(`DROP TABLE "consultant"`);
    await queryRunner.query(`DROP TABLE "consultant_account"`);
    await queryRunner.query(`DROP TABLE "permission"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e061236e6abd8503aa3890af94"`,
    );
    await queryRunner.query(`DROP TABLE "invitation"`);
    await queryRunner.query(`DROP TABLE "admin"`);
  }
}
