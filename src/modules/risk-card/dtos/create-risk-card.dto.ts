import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON>N<PERSON><PERSON>,
  IsOptional,
  IsUUID,
} from "class-validator";

import { RiskResponseType } from "@risk-card/risk-response-type.enum";

import { IsNotEmptyString } from "src/utils/decorators/is-not-empty-string.decorator";

export class CreateRiskCardDto {
  @IsNotEmptyString()
  title: string;

  @IsNotEmptyString()
  description: string;

  @IsNotEmptyString()
  @IsOptional()
  location?: string;

  @IsNotEmptyString()
  @IsOptional()
  driver?: string;

  @IsNotEmptyString()
  @IsOptional()
  impactSummary?: string;

  @IsNotEmptyString()
  @IsOptional()
  financialImpact?: string;

  @IsNotEmptyString()
  @IsOptional()
  nonFinancialImpact?: string;

  @IsNotEmptyString()
  @IsOptional()
  impactVelocity?: string;

  @IsNotEmptyString()
  @IsOptional()
  impactPersistence?: string;

  @IsNotEmptyString()
  @IsOptional()
  likelihoodSummary?: string;

  @IsNotEmptyString()
  @IsOptional()
  quantLikelihood?: string;

  @IsNotEmptyString()
  @IsOptional()
  qualitLikelihood?: string;

  @IsNotEmptyString()
  @IsOptional()
  controlSummary?: string;

  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  impactScore?: number;

  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  likelihoodScore?: number;

  @IsNumber()
  @IsNotEmpty()
  @IsOptional()
  controlScore?: number;

  @IsNotEmptyString()
  @IsOptional()
  department?: string;

  @IsEnum(RiskResponseType)
  @IsNotEmpty()
  @IsOptional()
  riskResponseType?: RiskResponseType;

  @IsUUID(4)
  @IsNotEmpty()
  riskGroupId: string;

  @IsUUID(4)
  @IsOptional()
  ownerId?: string;
}
