import { Button as EmailButton } from "@react-email/components";
import * as React from "react";
import type { ComponentPropsWithRef } from "react";

import {
  buttonContentColorsLut,
  buttonVariantsLut,
  type ButtonVariant,
} from "./buttonVariants";
import { Text } from "./Text";
import { mcn } from "./utils";

type ButtonSize = "md" | "sm";

interface ButtonProps
  extends Omit<ComponentPropsWithRef<typeof EmailButton>, "children"> {
  size?: ButtonSize;
  text: string;
  variant?: ButtonVariant;
}

const paddingsLut = {
  md: "px-3 py-2",
  sm: "px-2 py-1.5",
};

const textSizesLut = {
  md: "xs",
  sm: "2xs",
} as const;

const borderRadius = {
  md: "rounded-md",
  sm: "rounded-sm",
} as const;

export const Button = ({
  className,
  size = "md",
  text,
  variant = "primary",
  ...props
}: ButtonProps) => (
  <EmailButton
    {...props}
    className={mcn(
      "block",
      "text-center",
      borderRadius[size],
      buttonVariantsLut[variant],
      paddingsLut[size],
      className,
    )}
  >
    <Text
      color={buttonContentColorsLut[variant]}
      size={textSizesLut[size]}
      weight="semibold"
    >
      {text}
    </Text>
  </EmailButton>
);
