import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { AdminModule } from "src/modules/admin/admin.module";

import { InvitationController } from "./invitation.controller";
import { Invitation } from "./invitation.entity";
import { InvitationService } from "./invitation.service";
import { ClientAccount } from "../client-account/client-account.entity";
import { ClientAccountModule } from "../client-account/client-account.module";
import { CompanyModule } from "../company/company.module";
import { ConsultantModule } from "../consultant/consultant.module";
import { ConsultantAccount } from "../consultant-account/consultant-account.entity";
import { ConsultantAccountModule } from "../consultant-account/consultant-account.module";
import { UserModule } from "../user/user.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Invitation, ConsultantAccount, ClientAccount]),
    forwardRef(() => ConsultantAccountModule),
    forwardRef(() => ClientAccountModule),
    UserModule,
    AdminModule,
    ConsultantModule,
    CompanyModule,
  ],
  providers: [InvitationService],
  controllers: [InvitationController],
  exports: [InvitationService],
})
export class InvitationModule {}
