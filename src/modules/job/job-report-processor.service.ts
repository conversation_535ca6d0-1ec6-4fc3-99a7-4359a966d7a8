import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";

import { JobStatus } from "./enum/job-status.enum";
import { JobType } from "./enum/job-type.enum";
import { JobService } from "./job.service";
import { AIService } from "../ai/ai.service";
import { ContextReportService } from "../context-report/context-report.service";
import { RiskDomainService } from "../risk-domain/service/risk-domain.service";

@Injectable()
export class JobReportProcessorService implements OnModuleInit {
  private readonly logger = new Logger(ContextReportService.name);

  constructor(
    private jobService: JobService,
    private contextReportService: ContextReportService,
    private riskDomainService: RiskDomainService,
    private aiService: AIService,
  ) {}

  onModuleInit() {
    this.runBackgroundLoop();
  }

  private async runBackgroundLoop() {
    while (true) {
      const job = await this.jobService.getByStatus(JobStatus.Pending);
      if (job) {
        await this.jobService.update(job.id, {
          status: JobStatus.InProgress,
        });

        const jobInput = await this.jobService.unpackJobInput(job.input);

        try {
          if (jobInput.type === JobType.GenerateContextReport) {
            await this.updateContextReportWithAIResponses(jobInput.id, job.id);
          } else if (jobInput.type === JobType.GenerateRiskAssessment) {
            await this.riskDomainService.updateRiskCardWithAIResponses(
              jobInput.id,
              job.id,
            );
          }
        } catch (error) {
          await this.jobService.update(job.id, {
            status: JobStatus.Failed,
            result: { message: error },
          });
        }
      }
      await new Promise((res) => setTimeout(res, 5000));
    }
  }

  async updateContextReportWithAIResponses(contextId: string, jobId: string) {
    const results = [];

    const companyContextReport =
      await this.contextReportService.findOne(contextId);

    if (!companyContextReport) {
      throw new NotFoundException(
        `Context report with ID ${contextId} not found`,
      );
    }

    if (companyContextReport && companyContextReport.sections) {
      try {
        const ingestResponse = await this.aiService.request(
          `/ai/context-report/ingest-docs`,
          "POST",
          { company_id: companyContextReport.riskAssessment.company.id },
        );

        if (ingestResponse.success) {
          for (const section of companyContextReport.sections) {
            if (await this.jobService.isCanceled(jobId)) {
              this.logger.warn(
                "Job canceled. Stopping context report generation.",
              );
              return results;
            }
            try {
              if (section.prompt) {
                await new Promise((resolve) => setTimeout(resolve, 2000));

                const data = await this.aiService.request(
                  `/ai/context-report`,
                  "POST",
                  {
                    company_id: companyContextReport.riskAssessment.company.id,
                  },
                  { prompts: [{ id: section.id, prompt: section.prompt }] },
                );

                results.push(data);
              }
            } catch (err) {
              this.logger.error("Failed to fetch context report", err);
              throw new HttpException(
                "Failed to fetch context report",
                HttpStatus.BAD_GATEWAY,
              );
            }
          }
          const updatedReport =
            await this.contextReportService.updateSectionsWithAIResults(
              results,
              contextId,
            );

          if (updatedReport.length > 0) {
            await this.jobService.update(jobId, {
              status: JobStatus.Completed,
              result: {
                message: "Context report has been successfully generated.",
              },
            });
          }

          return updatedReport;
        }
      } catch (err) {
        this.logger.error("Error in creating context report", err);
        throw new HttpException(
          "Failed to ingest documents",
          HttpStatus.BAD_GATEWAY,
        );
      } finally {
        try {
          await this.aiService.request(
            `/ai/context-report/vector-db/${companyContextReport.riskAssessment.company.id}`,
            "DELETE",
          );
        } catch (err) {
          this.logger.error("Error in deleting vector DB", err);
        }
      }
    }
  }
}
