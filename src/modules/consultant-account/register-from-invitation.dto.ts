import { IsNotEmpty } from "class-validator";

import { IsPasswordMatch } from "src/utils/password/is-password-match.validator";
import { MeetsPasswordRequirements } from "src/utils/password/meets-password-requirements.validator";

export class RegisterFromInvitationDto {
  @IsNotEmpty()
  @MeetsPasswordRequirements()
  password: string;

  @IsNotEmpty()
  @IsPasswordMatch("password")
  confirmPassword: string;
}
