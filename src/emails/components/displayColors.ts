export type DisplayColor =
  | "accent-600"
  | "accent-700"
  | "accent-900"
  | "blue-950"
  | "brand-130"
  | "brand-50"
  | "brand"
  | "dark-400"
  | "dark-500"
  | "dark-600"
  | "dark-700"
  | "dark-800"
  | "dark-900"
  | "dark-950"
  | "error"
  | "white"
  | "yellow-950";

export const displayColorsLut = {
  "accent-600": "text-accent-600",
  "accent-700": "text-accent-700",
  "accent-900": "text-accent-900",
  "blue-950": "text-blue-950",
  brand: "text-brand",
  "brand-130": "text-brand-130",
  "brand-50": "text-brand-50",
  "dark-400": "text-dark-400",
  "dark-500": "text-dark-500",
  "dark-600": "text-dark-600",
  "dark-700": "text-dark-700",
  "dark-800": "text-dark-800",
  "dark-900": "text-dark-900",
  "dark-950": "text-dark-950",
  error: "text-error",
  white: "text-white",
  "yellow-950": "text-yellow-950",
} satisfies Record<DisplayColor, string>;
