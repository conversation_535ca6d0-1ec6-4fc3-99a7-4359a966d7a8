import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otE<PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
  IsUUI<PERSON>,
} from "class-validator";

import { IsNotEmptyString } from "@utils/decorators/is-not-empty-string.decorator";

import { ActionStatus } from "../action-status.enum";

export class CreateRiskResponseActionDto {
  @IsNotEmptyString()
  title: string;

  @IsString()
  @IsOptional()
  titleRes?: string;

  @IsString()
  @IsOptional()
  dueDate?: string;

  @IsString()
  @IsOptional()
  dueDateRes?: string;

  @IsString()
  @IsOptional()
  assignee?: string;

  @IsEnum(ActionStatus)
  status: ActionStatus;

  @IsUUID(4)
  @IsNotEmpty()
  riskCardId: string;
}
