import { Body, Controller, Get, Post } from "@nestjs/common";

import { Admin } from "./admin.entity";
import { AdminService } from "./admin.service";
import { CreateAdminDto } from "./dtos/create-admin.dto";

@Controller("admin")
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get()
  async findAll(): Promise<Admin[]> {
    return await this.adminService.findAll();
  }

  @Post()
  async create(@Body() createAdminDto: CreateAdminDto): Promise<Admin> {
    return await this.adminService.create(createAdminDto);
  }

  // @Get(":id")
  // async find(
  //   @Param("id", new ParseUUIDPipe({ version: "4" }))
  //   id: string,
  // ): Promise<Admin> {
  //   return await this.adminService.findOrFail(id);
  // }

  // @Patch(":id")
  // async update(
  //   @Param("id", new ParseUUIDPipe({ version: "4" }))
  //   id: string,
  //   @Body() updateAdminDto: UpdateAdminDto,
  // ): Promise<Admin> {
  //   return await this.adminService.update(id, updateAdminDto);
  // }

  // @Delete(":id")
  // @HttpCode(204)
  // async delete(
  //   @Param("id", new ParseUUIDPipe({ version: "4" }))
  //   id: string,
  // ): Promise<void> {
  //   await this.adminService.delete(id);
  //   return;
  // }
}
