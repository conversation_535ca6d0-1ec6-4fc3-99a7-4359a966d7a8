import { Expose, Transform, Type } from "class-transformer";

import { OrganizationType } from "src/utils/organization-type";

import { Invitation } from "../invitation.entity";
import {
  InvitationInvalidReason,
  ValidationResult,
} from "../invitation.service";

class OrganizationDto {
  id: string;
  logoUrl: string | null;
  name: string;
  type: OrganizationType;
}

class UserDto {
  id: string;
  name: string;
  email: string;
  hasAccount: string;
}

class InvitedByAdminDto {
  id: string;
  firstName: string;
  lastName: string;
}

class InvitedByUserDto {
  id: string;
  name: string;
  organization: OrganizationDto;
}

class InvitationDto {
  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) => {
      if (obj.company) {
        return {
          id: obj.company.id,
          logoUrl: obj.company.logoUrl,
          name: obj.company.name,
          type: OrganizationType.Company,
        };
      }

      if (obj.consultant) {
        return {
          id: obj.consultant.id,
          logoUrl: obj.consultant.logoUrl,
          name: obj.consultant.name,
          type: OrganizationType.Consultant,
        };
      }

      return null;
    },
    { toClassOnly: true },
  )
  invitedToOrganization: OrganizationDto | null;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) => {
      return {
        id: obj.id,
        name: obj.name,
        email: obj.email,
        hasAccount: false,
      };
    },
    { toClassOnly: true },
  )
  user: UserDto;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) => {
      if (obj.createdByAdmin) {
        return {
          id: obj.createdByAdmin.id,
          firstName: obj.createdByAdmin.firstName,
          lastName: obj.createdByAdmin.lastName,
        };
      }

      return null;
    },
    { toClassOnly: true },
  )
  invitedByAdmin: InvitedByAdminDto | null;

  @Expose()
  @Transform(
    ({ obj }: { obj: Invitation }) => {
      if (obj.createdByConsultantAccount) {
        return {
          id: obj.createdByConsultantAccount.id,
          name: obj.createdByConsultantAccount.name,
          organization: {
            id: obj.createdByConsultantAccount.consultant.id,
            logoUrl: obj.createdByConsultantAccount.consultant.logoUrl,
            name: obj.createdByConsultantAccount.consultant.name,
            type: OrganizationType.Consultant,
          },
        };
      }

      if (obj.createdByClientAccount) {
        return {
          id: obj.createdByClientAccount.id,
          name: obj.createdByClientAccount.company.name,
          organization: {
            id: obj.createdByClientAccount.company.id,
            logoUrl: obj.createdByClientAccount.company.logoUrl,
            name: obj.createdByClientAccount.company.name,
            type: OrganizationType.Company,
          },
        };
      }

      return null;
    },
    { toClassOnly: true },
  )
  invitedByUser: InvitedByUserDto | null;
}

export class ValidateInvitationResponseDto {
  @Expose()
  @Type(() => InvitationDto)
  invitation: InvitationDto;

  @Expose()
  reason: InvitationInvalidReason | null;

  @Expose()
  isValid: boolean;

  @Expose()
  @Transform(
    ({ obj }: { obj: ValidationResult }) => {
      if (!obj.invitation) {
        return null;
      }

      return {
        id: obj.invitation.id,
        name: obj.invitation.name,
        email: obj.invitation.email,
        isRegistered: !!obj.existingUser,
      };
    },
    {
      toClassOnly: true,
    },
  )
  user: UserDto | null;
}
