import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUserLockout1756306318131 implements MigrationInterface {
  name = "AddUserLockout1756306318131";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "admin" ADD "failed_login_attempts" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" ADD "last_failed_login_attempt" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "failed_login_attempts" integer NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "last_failed_login_attempt" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "last_failed_login_attempt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "failed_login_attempts"`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" DROP COLUMN "last_failed_login_attempt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "admin" DROP COLUMN "failed_login_attempts"`,
    );
  }
}
