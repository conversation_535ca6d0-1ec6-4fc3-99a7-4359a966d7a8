import { CreateSectionDto } from "../section/dtos/create-section.dto";

export const getDefaultSections = (
  contextReportId: string,
  companyName: string,
  companyShortName: string | null,
  consultantName: string,
  riskExercise: string,
): Partial<CreateSectionDto>[] => {
  if (!companyShortName) {
    companyShortName = companyName;
  }

  const introductionText = `
    <p>
      The Context Report serves as the foundation for conducting the analysis of ${companyName}' business activities against and potential ESG risks, opportunities and impacts (ROIs) associated with those business activities. 
      The Context Report was prepared for ${companyName} as part of a ${riskExercise}.
    </p>

    <p>
      This document should be viewed as an internal working document for the company and the supporting teams to generate a shared understanding of ${companyShortName}'s business and the external context in which it operates. 
      From this document, a subsequent risk assessment will be completed, with the confidence that there is an accepted foundational view of relevant risk matters. 
      To establish this view, subject matter experts (SMEs) at ${companyShortName} and ${consultantName} will be engaged in the creation of this report. 
      In addition, this report supports the assessment of the process followed to establish Double Materiality for ${companyName} as part of assurance exercises.
    </p>

    <p>This context report is intended to provide the company background information required to:</p>

    <ul>
      <li>
        Surface key risks, opportunities, and impacts by identifying actual and potential issues in Step 2 and guiding further research on scope and scale in Step 3.
      </li>
      <li>
        Engage stakeholders meaningfully by identifying and involving internal and external voices during Steps 2 and 3.
      </li>
      <li>
        Support prioritization by informing the description of the most significant material topics to be addressed in Step 4.
      </li>
    </ul>
    <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
      <thead>
        <tr>
          <th>Step 1</th>
          <th>Step 2</th>
          <th>Step 3</th>
          <th>Step 4</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Understand company context</td>
          <td>Identify actual and potential risks, opportunities, and impacts (ROIs).</td>
          <td>Conduct the ROI analysis through a traditional assessment model.</td>
          <td>Prioritize the most significant ROIs.</td>
        </tr>
      </tbody>
    </table>
  `;
  return [
    {
      title: "Introduction",
      level: 1,
      position: 1,
      contextReportId,
      children: [],
      text: introductionText,
    },

    {
      title: "Organizational overview",
      level: 1,
      position: 2,
      prompt:
        "Organizational overview First use full name like PTC Therapeutics, then just the word company.",
      contextReportId: contextReportId,
      children: [
        {
          title: "Purpose, Mission, and Strategic Plan and Business Objectives",
          level: 2,
          position: 1,
          contextReportId,
          children: [
            {
              title: "Purpose",
              level: 3,
              position: 1,
              contextReportId,
              children: [],
              prompt:
                "Purpose: What is the overarching purpose of the company? Do not use full name of the company, just the word company",
            },
            {
              title: "Mission",
              level: 3,
              position: 2,
              contextReportId,
              children: [],
              prompt:
                "Mission Statement: What is the organization's mission statement? Do not use full name of the company, just the word company",
            },
            {
              title: "Strategic Plan and Business Objectives",
              level: 3,
              position: 3,
              contextReportId,
              children: [],
              prompt:
                "Strategic Plan and Business Objectives: What are the current strategic priorities, long-range goals, and key initiatives? Only answer using content from the provided context, which includes documents flagged as 'always_include' equals True. Do not use full name of the company, justthe word company instead PTC or PRC Therapeuticals",
            },
          ],
        },
        {
          title: "Activities and Geographic Footprint",
          level: 2,
          position: 2,
          contextReportId,
          children: [
            {
              title: "Business Activities",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Business Activities: What are the organization's core and support functions?  Do not use full name of the company, just the word company",
            },
            {
              title: "Manufacturing Activities",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "Manufacturing Activities: What are the organization's manufacturing locations and processes?  Do not use full name of the company, just the word company",
            },
            {
              title: "Purchase Activities",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "Purchase Activities: What are the procurement processes and suppliers? Do not use full name of the company, just the word company",
            },
            {
              title: "Corporate and Operational Locations",
              level: 3,
              position: 4,
              contextReportId,
              prompt:
                "Corporate Headquarters and Corporate Locations: List all headquarters and locations with address and country",
            },
          ],
        },
        {
          title: "Products, Services, Markets",
          level: 2,
          position: 3,
          contextReportId,
          children: [
            {
              title: "Products and Services",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Products and Services: What are the most mentioned company products? List all of them with their indication and approval.",
            },
            {
              title: "Markets",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "Markets: What are the key geographies and market segments served? Do not use full name of the company, just the word company",
            },
            {
              title: "Key Business Characteristics",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "Key Business Characteristics: What are the industry-specific differentiators?",
            },
          ],
        },
        {
          title: "Employees and Demographics",
          level: 2,
          position: 4,
          contextReportId,
          children: [
            {
              title: "Total Employees",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Total Employees: What is the organization's workforce size? How many employees headquarters have, and how many other locations?",
            },
            {
              title: "Employee Demographics",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "Employee Demographics: What is the breakdown of FTE vs part-time vs contractors, etc.?",
            },
          ],
        },
        {
          title: "Business Relationships",
          level: 2,
          position: 5,
          contextReportId,
          children: [
            {
              title: "Business Partners and Contractors",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Business Partners and Contractors: Who are the major third-party relationships and name them? Who are the partners and who are collaborators in licensing, marketing, commercial?",
            },
          ],
        },
        {
          title: "Governance and Leadership",
          level: 2,
          position: 6,
          contextReportId,
          children: [
            {
              title: "Governance Structure",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Governance Structure:  What is the organizational decision-making framework?",
            },
            {
              title: "Leadership Capabilities",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "Leadership Capabilities: What are the leadership capabilities and succession planning practices?",
            },
            {
              title: "Benchmarks",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "Benchmarks: Who does the organization benchmark against?",
            },
          ],
        },
        {
          title: "Information Systems",
          level: 2,
          position: 7,
          contextReportId,
          children: [
            {
              title: "Data Infrastructure, Flows, and Security Practices",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Data Infrastructure, Flows, and Security Practices:  What are the data infrastructure, integration, and security practices?",
            },
          ],
        },
      ],
    },
    {
      title: "External Context",
      level: 1,
      position: 3,
      contextReportId,
      children: [
        {
          title: "Political Factors",
          level: 2,
          position: 1,
          contextReportId,
          prompt:
            "Political Factors:  What aspects of the political environment are impacting operations of the company? Give me detailed answer and examples as well",
        },
        {
          title: "Economic Factors",
          level: 2,
          position: 2,
          contextReportId,
          prompt:
            "Economic Factors: What are the macroeconomic and industry economic conditions? Give me detailed answer and examples as well",
        },
        {
          title: "Social/Cultural Factors",
          level: 2,
          position: 3,
          contextReportId,
          prompt:
            "Social/Cultural Factors: What demographic shifts and social trends are relevant? Give me detailed answer and examples as well",
        },
        {
          title: "Technological Factors",
          level: 2,
          position: 4,
          contextReportId,
          prompt:
            "Technological Factors:  What disruptive technologies and innovations affect the industry? Give me detailed answer and examples as well",
        },
        {
          title: "Legal/Regulatory Factors",
          level: 2,
          position: 5,
          contextReportId,
          prompt:
            "Legal/Regulatory Factors:  What compliance requirements and regulations must the organization meet, e.g. Overview of Biopharmaceutical Regulation, FDA Oversight and Drug Approval Process, Post-Approval Requirements and Specialized Programs, Global and Market Access Considerations? Give me detailed answer and examples as well",
        },
        {
          title: "Environmental Factors",
          level: 2,
          position: 6,
          contextReportId,
          prompt:
            "Environmental Factors:  What are the sustainability concerns and climate impacts?  Give me detailed answer and examples as well",
        },
        {
          title: "Stakeholder Expectations and External Perceptions",
          level: 2,
          position: 7,
          contextReportId,
          prompt:
            "Stakeholder Expectations and External Perceptions: What are the external perceptions and expectations from stakeholders?  Give me detailed answer and examples as well",
        },
      ],
    },
    {
      title: "Regulatory and Voluntary Frameworks",
      level: 1,
      position: 4,
      contextReportId,
      children: [
        {
          title: "Authoritative Instruments",
          level: 2,
          position: 1,
          contextReportId,
          children: [
            {
              title: "UN Guiding Principles on Business and Human Rights",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "UN Guiding Principles on Business and Human Rights: To what extent does the organization adhere to the UN Guiding Principles on Business and Human Rights?",
            },
            {
              title: "OECD Multinational Guidelines",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "OECD Multinational Guidelines: How has the organization implemented the OECD Guidelines for Multinational Enterprises?",
            },
            {
              title: "ILO Core Labor Standards",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "ILO Core Labor Standards: Is the organization in compliance with the ILO Core Labor Standards? How is this monitored?",
            },
            {
              title: "WHO and Health-Specific International Guidelines",
              level: 3,
              position: 4,
              contextReportId,
              prompt:
                "WHO and Health-Specific International Guidelines: How does the organization align with WHO guidelines and other health-specific international frameworks?",
            },
            {
              title: "GDPR and Global Data Privacy Frameworks",
              level: 3,
              position: 5,
              contextReportId,
              prompt:
                "GDPR and Global Data Privacy Frameworks: What measures are in place to ensure compliance with GDPR and other global data privacy frameworks?",
            },
            {
              title: "Paris Agreement and Climate Disclosure Protocols",
              level: 3,
              position: 6,
              contextReportId,
              prompt:
                "Paris Agreement and Climate Disclosure Protocols: How is the organization adhering to the Paris Agreement and relevant climate disclosure protocols?",
            },
          ],
        },
        {
          title: "Sector Regulations and Standards",
          level: 2,
          position: 2,
          contextReportId,
          children: [
            {
              title: "SASB Biotechnology & Pharmaceuticals Standards",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "SASB Biotechnology: How does the organization comply with SASB standards for the Biotechnology and Pharmaceuticals sector?",
            },
            {
              title:
                "ISSB IFRS S1 & S2 (Sustainability and Climate Disclosures)",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "ISSB IFRS S1 & S2 (Sustainability and Climate Disclosures): Does the document reference or align with ISSB's IFRS S1 or S2 standards related to sustainability and climate-related disclosures? If so, what specific elements or requirements are addressed?",
            },
            {
              title: "National and regional regulations",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "National and regional regulations: How does the organization ensure compliance with national and regional ESG, financial, tax, environmental, health, and labor regulations?",
            },
          ],
        },
        {
          title: "Organizational Certifications",
          level: 2,
          position: 3,
          contextReportId,
          prompt:
            "WHO and Health-Specific International Guidelines: How does the organization align with WHO guidelines and other health-specific international frameworks?",
        },
      ],
    },
    {
      title: "Organizational Dependencies & Capitals",
      level: 1,
      position: 5,
      contextReportId,
      children: [
        {
          title: "Natural Capital",
          level: 2,
          position: 1,
          contextReportId,
          prompt:
            "Natural Capital: How does the organization rely on natural resources such as water, biodiversity, clean air, and minerals in its operations, supply chain, or product development?",
        },
        {
          title: "Human Capital",
          level: 2,
          position: 2,
          contextReportId,
          prompt:
            "Human Capital: What programs, policies, or strategies does the organization have in place to ensure employee competency, ongoing training, and leadership development across all levels?",
        },
        {
          title: "Social and Relationship Capital",
          level: 2,
          position: 3,
          contextReportId,
          children: [
            {
              title: "Trust with stakeholders",
              level: 3,
              position: 1,
              contextReportId,
              prompt:
                "Trust with stakeholders: How does the organization build, maintain, and measure trust with key stakeholders such as employees, customers, investors, regulators, and communities?",
            },
            {
              title: "Cultural alignment",
              level: 3,
              position: 2,
              contextReportId,
              prompt:
                "Cultural alignment: In what ways does the organization ensure cultural alignment across different business units, geographies, and with external partners?",
            },
            {
              title: "Partnerships and reputation",
              level: 3,
              position: 3,
              contextReportId,
              prompt:
                "Partnerships and reputation: How does the organization manage strategic partnerships and maintain a strong reputation across markets?",
            },
          ],
        },
      ],
    },
  ];
};
