import { HttpModule } from "@nestjs/axios";
import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { ContextReportController } from "./context-report.controller";
import { ContextReport } from "./context-report.entity";
import { ContextReportService } from "./context-report.service";
import { CompanyModule } from "../company/company.module";
import { RiskAssessmentModule } from "../risk-assessment/risk-assessment.module";
import { RiskDomainModule } from "../risk-domain/risk-domain.module";
import { SectionModule } from "../section/section.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([ContextReport]),
    SectionModule,
    HttpModule,
    CompanyModule,
    RiskDomainModule,
    forwardRef(() => RiskAssessmentModule),
  ],
  controllers: [ContextReportController],
  exports: [ContextReportService],
  providers: [ContextReportService],
})
export class ContextReportModule {}
