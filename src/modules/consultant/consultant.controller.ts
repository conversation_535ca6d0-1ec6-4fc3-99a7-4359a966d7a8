import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
} from "@nestjs/common";
import { plainToInstance } from "class-transformer";

import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { Consultant } from "./consultant.entity";
import { ConsultantService } from "./consultant.service";
import { ConsultantResponseDto } from "./dtos/consultant-response-dto";
import { ConsultantsListDto } from "./dtos/consultants-response-dto";
import { CreateConsultantDto } from "./dtos/create-consultant.dto";

@Controller("consultant")
export class ConsultantController {
  constructor(private readonly consultantService: ConsultantService) {}

  @Get()
  @Roles(Role.Admin)
  async findAll(): Promise<ConsultantsListDto> {
    const consultants = await this.consultantService.findAll();

    return plainToInstance(
      ConsultantsListDto,
      { consultants },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  @Get(":id")
  @Roles(Role.Admin)
  async find(
    @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  ): Promise<ConsultantResponseDto> {
    const consultant = await this.consultantService.findOrFail(id);

    return plainToInstance(ConsultantResponseDto, consultant, {
      excludeExtraneousValues: true,
    });
  }

  @Post()
  @Roles(Role.Admin)
  async create(
    @Body() createConsultantDto: CreateConsultantDto,
  ): Promise<Consultant> {
    return await this.consultantService.create(createConsultantDto);
  }
}
