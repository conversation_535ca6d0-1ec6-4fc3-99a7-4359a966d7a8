import { ClassSerializerInterceptor, Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { APP_INTERCEPTOR, Reflector } from "@nestjs/core";
import { TypeOrmModule } from "@nestjs/typeorm";

import { AdminModule } from "./modules/admin/admin.module";
import { AIModule } from "./modules/ai/ai-module";
import { AuthModule } from "./modules/auth/auth.module";
import { ClientAccountModule } from "./modules/client-account/client-account.module";
import { CompanyModule } from "./modules/company/company.module";
import { ConsultantModule } from "./modules/consultant/consultant.module";
import { ConsultantAccountModule } from "./modules/consultant-account/consultant-account.module";
import { ContextReportModule } from "./modules/context-report/context-report.module";
import { DocumentModule } from "./modules/document/document.module";
import { InvitationModule } from "./modules/invitation/invitation.module";
import { JobModule } from "./modules/job/job.module";
import { PermissionModule } from "./modules/permission/permission.module";
import { RiskAssessmentModule } from "./modules/risk-assessment/risk-assessment.module";
import { RiskCardModule } from "./modules/risk-card/risk-card.module";
import { RiskDomainModule } from "./modules/risk-domain/risk-domain.module";
import { RiskGroupModule } from "./modules/risk-group/risk-group.module";
import { RiskResponseActionModule } from "./modules/risk-response-action/risk-response-action.module";
import { S3Module } from "./modules/s3/s3.module";
import { SectionModule } from "./modules/section/section.module";
import { UserModule } from "./modules/user/user.module";
import { ValidationPipeFactory } from "./utils/validation-pipe.factory";

export const ClassSerializerFactory = {
  provide: APP_INTERCEPTOR,
  useFactory: () => {
    return new ClassSerializerInterceptor(new Reflector());
  },
};

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: "postgres",
        host: configService.get<string>("DB_HOST"),
        port: configService.get<number>("DB_PORT") || 5432,
        username: configService.get<string>("DB_USER"),
        password: configService.get<string>("DB_PASS") || "",
        database: configService.get<string>("DB_NAME"),
        entities: [__dirname + "/**/*.entity.{ts,js}"],
        migrations: [__dirname + "/db/migrations/*.{ts,js}"],
        synchronize: false,
        autoLoadEntities: true,
        migrationsRun: true,
      }),
    }),
    AuthModule,
    AdminModule,
    UserModule,
    InvitationModule,
    ConsultantAccountModule,
    ClientAccountModule,
    CompanyModule,
    ConsultantModule,
    PermissionModule,
    ContextReportModule,
    RiskAssessmentModule,
    RiskGroupModule,
    RiskCardModule,
    SectionModule,
    DocumentModule,
    S3Module,
    JobModule,
    RiskDomainModule,
    AIModule,
    RiskResponseActionModule,
  ],
  providers: [ValidationPipeFactory, ClassSerializerFactory],
})
export class AppModule {}
