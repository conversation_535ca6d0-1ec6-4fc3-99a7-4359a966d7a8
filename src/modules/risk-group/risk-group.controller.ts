import { Body, Controller, Post } from "@nestjs/common";

import { Role } from "src/utils/roles/role";
import { Roles } from "src/utils/roles/roles.decorator";

import { CreateRiskGroupDto } from "./dtos/create-risk-group.dto";
import { RiskGroup } from "./risk-group.entity";
import { RiskGroupService } from "./risk-group.service";

@Controller("risk-group")
export class RiskGroupController {
  constructor(private readonly riskGroupService: RiskGroupService) {}

  // TODO not used by frontend
  @Post()
  @Roles(Role.Admin)
  async create(
    @Body() createRiskGroupDto: CreateRiskGroupDto,
  ): Promise<RiskGroup> {
    return await this.riskGroupService.create(createRiskGroupDto);
  }

  // @Patch(":id")
  // async update(
  //   @Param("id", new ParseUUIDPipe({ version: "4" })) id: string,
  //   @Body() updateRiskGroupDto: UpdateRiskGroupDto,
  // ): Promise<RiskGroup> {
  //   return await this.riskGroupService.update(id, updateRiskGroupDto);
  // }

  //This is an endpoint for only testing purposes
  @Post("/default")
  async createDefault(
    @Body() createRiskGroupDto: { riskAssessmentId: string },
  ): Promise<RiskGroup[]> {
    return await this.riskGroupService.createDefaultGroups(
      createRiskGroupDto.riskAssessmentId,
    );
  }
}
