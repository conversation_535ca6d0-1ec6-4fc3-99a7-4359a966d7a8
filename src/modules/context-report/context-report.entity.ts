import { IsBoolean } from "class-validator";
import { Column, <PERSON><PERSON><PERSON>, OneToMany, OneToOne } from "typeorm";

import { BaseEntity } from "src/utils/base.entity";

import { RiskAssessment } from "../risk-assessment/risk-assessment.entity";
import { Section } from "../section/section.entity";

@Entity()
export class ContextReport extends BaseEntity {
  @Column({ name: "is_locked_for_client", default: true })
  @IsBoolean()
  isLockedForClient: boolean;

  @Column({ name: "consultant_name", default: "Eastward" })
  consultantName: string;

  @Column({ name: "risk_exercise", default: "Single Materiality Assessment" })
  riskExercise: string;

  @OneToOne(
    () => RiskAssessment,
    (riskAssessment) => riskAssessment.contextReport,
    { onDelete: "CASCADE" },
  )
  riskAssessment: RiskAssessment;

  @OneToMany(() => Section, (section) => section.contextReport, {
    cascade: true,
  })
  sections: Section[];
}
