import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from "class-validator";

import { MeetsPasswordRequirements } from "src/utils/password/meets-password-requirements.validator";

export class CreateAdminDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @MeetsPasswordRequirements()
  password: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;
}
