import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateRiskResponseTable1759261416134
  implements MigrationInterface
{
  name = "CreateRiskResponseActionTable1759261416134";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."risk_response_action_status_enum" AS ENUM('not_started', 'on_target', 'completed', 'at_risk', 'delayed')`,
    );
    await queryRunner.query(
      `CREATE TABLE "risk_response_action" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "title" character varying NOT NULL, "title_res" character varying, "due_date" character varying, "due_date_res" character varying, "assignee" character varying, "status" "public"."risk_response_action_status_enum" NOT NULL DEFAULT 'not_started', "risk_card" uuid, CONSTRAINT "PK_f4cfe7f2b59566407ede823857a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."risk_card_ai_response_risk_response_type_enum" AS ENUM('mitigate', 'transfer', 'avoid', 'monitor')`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" ADD "risk_response_type" "public"."risk_card_ai_response_risk_response_type_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."risk_card_risk_response_type_enum" AS ENUM('mitigate', 'transfer', 'avoid', 'monitor')`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" ADD "risk_response_type" "public"."risk_card_risk_response_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_response_action" ADD CONSTRAINT "FK_8e285ab06331bbd602e2567c245" FOREIGN KEY ("risk_card") REFERENCES "risk_card"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "risk_response_action" DROP CONSTRAINT "FK_8e285ab06331bbd602e2567c245"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card" DROP COLUMN "risk_response_type"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."risk_card_risk_response_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "risk_card_ai_response" DROP COLUMN "risk_response_type"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."risk_card_ai_response_risk_response_type_enum"`,
    );
    await queryRunner.query(`DROP TABLE "risk_response_action"`);
    await queryRunner.query(
      `DROP TYPE "public"."risk_response_action_status_enum"`,
    );
  }
}
