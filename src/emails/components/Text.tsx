import { Text as EmailText } from "@react-email/components";
import * as React from "react";

import { DisplayColor, displayColorsLut } from "./displayColors";
import { mcn } from "./utils";

interface TextProps extends React.ComponentPropsWithRef<"p"> {
  as?: "span";
  color?: DisplayColor;
  isLink?: boolean;
  isUppercase?: boolean;
  size?: TextSize;
  weight?: TextWeight;
}

export type TextSize =
  | "2xl"
  | "2xs"
  | "3xl"
  | "3xs"
  | "lg"
  | "md"
  | "sm"
  | "xl"
  | "xs";

export type TextWeight = "bold" | "regular" | "semibold";

const textSizesLut = {
  "2xl": "text-[32px] leading-[48px]",
  "2xs": "text-[12px] leading-[16px]",
  "3xl": "text-[36px] leading-[56px]",
  "3xs": "text-[10px] leading-[14px]",
  lg: "text-[21px] leading-[36px]",
  md: "text-[18px] leading-[32px]",
  sm: "text-[16px] leading-[24px]",
  xl: "text-[27px] leading-[40px]",
  xs: "text-[14px] leading-[20px]",
} satisfies Record<TextSize, string>;

const textWeightsLut = {
  bold: "font-bold",
  regular: "font-normal",
  semibold: "font-semibold",
} satisfies Record<TextWeight, string>;

export const Text = ({
  as,
  children,
  className,
  color = "dark-950",
  isLink,
  isUppercase,
  size = "md",
  weight,
  ...props
}: TextProps) => {
  const Element = as === "span" ? "span" : EmailText;

  return (
    <Element
      className={mcn(
        "m-0",
        isLink && "underline",
        textSizesLut[size],
        displayColorsLut[color],
        weight && textWeightsLut[weight],
        isUppercase && "uppercase",
        className,
      )}
      {...props}
    >
      {children}
    </Element>
  );
};
