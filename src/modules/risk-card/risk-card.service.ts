import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { ClientAccountService } from "@client-account/client-account.service";

import { CreateRiskCardDto } from "./dtos/create-risk-card.dto";
import { UpdateRiskCardDto } from "./dtos/update-risk-card.dto";
import { RiskCard } from "./risk-card.entity";
import { risks } from "../risk-assessment/default-risks";
import { RiskGroupService } from "../risk-group/risk-group.service";

@Injectable()
export class RiskCardService {
  constructor(
    @InjectRepository(RiskCard)
    private riskCardRepository: Repository<RiskCard>,
    private riskGroupService: RiskGroupService,
    private clientAccountService: ClientAccountService,
  ) {}

  async create(createRiskCardDto: CreateRiskCardDto): Promise<RiskCard> {
    const riskGroup = await this.riskGroupService.findOrFail(
      createRiskCardDto.riskGroupId,
    );

    return await this.riskCardRepository.save(
      this.riskCardRepository.create({
        title: createRiskCardDto.title,
        description: createRiskCardDto.description,
        location: createRiskCardDto.location,
        driver: createRiskCardDto.driver,
        impactSummary: createRiskCardDto.impactSummary,
        financialImpact: createRiskCardDto.financialImpact,
        nonFinancialImpact: createRiskCardDto.nonFinancialImpact,
        impactVelocity: createRiskCardDto.impactVelocity,
        impactPersistence: createRiskCardDto.impactPersistence,
        likelihoodSummary: createRiskCardDto.likelihoodSummary,
        quantLikelihood: createRiskCardDto.quantLikelihood,
        qualitLikelihood: createRiskCardDto.qualitLikelihood,
        controlSummary: createRiskCardDto.controlSummary,
        impactScore: createRiskCardDto.impactScore,
        likelihoodScore: createRiskCardDto.likelihoodScore,
        controlScore: createRiskCardDto.controlScore,
        maxScore: riskGroup.riskAssessment.company.riskMaxScore,
        department: createRiskCardDto.department,
        riskResponseType: createRiskCardDto.riskResponseType,
        owner: { id: createRiskCardDto.ownerId },
        riskGroup: { id: createRiskCardDto.riskGroupId },
      }),
    );
  }

  async find(id: string): Promise<RiskCard | null> {
    return await this.riskCardRepository.findOne({
      where: { id },
      relations: {
        riskGroup: true,
        riskControls: true,
        owner: true,
        riskResponseActions: true,
      },
    });
  }

  async findOrFail(id: string): Promise<RiskCard> {
    const riskCard = await this.find(id);

    if (!riskCard) {
      throw new NotFoundException("Risk card not found.");
    }

    return riskCard;
  }

  async update(
    id: string,
    updateRiskCardDto: UpdateRiskCardDto,
  ): Promise<RiskCard> {
    await this.findOrFail(id);

    const { riskGroupId, ownerId, ...rest } = updateRiskCardDto;

    if (riskGroupId) {
      await this.riskGroupService.findOrFail(riskGroupId);
    }

    if (ownerId) {
      await this.clientAccountService.findOrFail(ownerId);
    }

    await this.riskCardRepository.update(
      { id },
      {
        ...rest,
        ...(riskGroupId ? { riskGroup: { id: riskGroupId } } : {}),
        ...(ownerId ? { owner: { id: ownerId } } : {}),
      },
    );

    return await this.findOrFail(id);
  }

  async findRelatedRiskCards(id: string): Promise<RiskCard[]> {
    await this.findOrFail(id);

    const query = this.riskCardRepository
      .createQueryBuilder("rc")
      .innerJoin(
        "related_risk_card",
        "rrc",
        "rrc.risk_card_id = rc.id OR rrc.related_risk_card_id = rc.id",
      )
      .where(":cardId IN (rrc.risk_card_id, rrc.related_risk_card_id)", {
        cardId: id,
      })
      .andWhere("rc.id != :cardId", { cardId: id })
      .orderBy("rc.createdAt", "ASC");

    return await query.getMany();
  }

  async createRiskDefaultCards(riskAssessmentId: string): Promise<RiskCard[]> {
    let riskCards = [] as RiskCard[];
    const riskGroups =
      await this.riskGroupService.findByRiskAssessment(riskAssessmentId);

    if (riskGroups && riskGroups.length > 0) {
      const groupMap = Object.fromEntries(
        riskGroups.map((g) => [g.name, g.id]),
      );

      for (const riskCard of risks) {
        const createdRiskCard = await this.create({
          title: riskCard.title,
          description: riskCard.description,
          riskGroupId: groupMap[riskCard.groupName],
        });

        if (createdRiskCard) {
          riskCards.push(createdRiskCard);
        }
      }
    }
    return riskCards;
  }
}
