import { Expose, Transform, Type } from "class-transformer";

import { ClientAccountResponseDto } from "@client-account/client-account-response.dto";

import { RiskResponseType } from "@risk-card/risk-response-type.enum";

import { RiskCard } from "../risk-card.entity";

class RiskControlDto {
  @Expose()
  id: string;

  @Expose()
  title: string;
}

class RelatedRiskDto {
  @Expose()
  id: string;

  @Expose()
  title: string;
}

class RiskResponseActionDto {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  dueDate: string | null;

  @Expose()
  @Type(() => ClientAccountResponseDto)
  assignee: ClientAccountResponseDto | null;
}
export class RiskCardResponseDto {
  @Expose()
  id: string;

  @Expose()
  @Transform(({ obj }: { obj: RiskCard }) => obj.riskGroup.riskAssessmentId, {
    toClassOnly: true,
  })
  assessmentId: string;

  @Expose()
  title: string;

  @Expose()
  description: string;

  @Expose()
  location: string | null;

  @Expose()
  driver: string | null;

  @Expose()
  impactSummary: string | null;

  @Expose()
  likelihoodSummary: string | null;

  @Expose()
  controlSummary: string | null;

  @Expose()
  impactScore: number;

  @Expose()
  likelihoodScore: number;

  @Expose()
  controlScore: number;

  @Expose()
  generalScore: number;

  @Expose()
  maxScore: number;

  @Expose()
  financialImpact: string | null;

  @Expose()
  nonFinancialImpact: string | null;

  @Expose()
  impactVelocity: string | null;

  @Expose()
  impactPersistence: string | null;

  @Expose()
  quantLikelihood: string | null;

  @Expose()
  qualitLikelihood: string | null;

  @Expose()
  @Type(() => RiskControlDto)
  riskControls: RiskControlDto[];

  @Expose()
  @Type(() => RelatedRiskDto)
  relatedRiskCards: RelatedRiskDto[];

  @Expose()
  @Transform(({ obj }: { obj: RiskCard }) => obj.riskGroup.name, {
    toClassOnly: true,
  })
  groupName: string;

  @Expose()
  department: string | null;

  @Expose()
  @Type(() => ClientAccountResponseDto)
  owner: ClientAccountResponseDto | null;

  @Expose()
  riskResponseType: RiskResponseType | null;

  @Expose()
  @Type(() => RiskResponseActionDto)
  riskResponseActions: RiskResponseActionDto[];
}
