import {
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-local";

import { User } from "src/modules/user/user.entity";

import { AuthService } from "../services/auth.service";

@Injectable()
export class LocalUserStrategy extends PassportStrategy(
  Strategy,
  "local-user",
) {
  constructor(private authService: AuthService) {
    super({ usernameField: "email" });
  }

  async validate(
    email: string,
    password: string,
  ): Promise<Omit<User, "password">> {
    const res = await this.authService.validateUser(email, password);

    if (res.entity) {
      return res.entity;
    }

    if (res.reason === "locked") {
      throw new ForbiddenException();
    }

    throw new UnauthorizedException();
  }
}
