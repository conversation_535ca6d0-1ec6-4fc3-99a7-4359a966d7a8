import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { AxiosRequestConfig, Method } from "axios";
import { firstValueFrom } from "rxjs";

@Injectable()
export class AIService {
  private readonly AIApiUrl: string;
  private readonly logger = new Logger(AIService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpService,
  ) {
    this.AIApiUrl =
      this.configService.get<string>("AI_API_URL") || "http://localhost:8000";
  }

  async request<T = any>(
    endpoint: string,
    method: Method = "GET",
    params?: Record<string, any>,
    body?: Record<string, any>,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const url = `${this.AIApiUrl}${endpoint}`;

    const axiosConfig: AxiosRequestConfig = {
      method,
      url,
      params,
      data: body,
      ...config,
    };

    try {
      const response = await firstValueFrom(this.http.request<T>(axiosConfig));
      return response.data;
    } catch (error) {
      this.logger.error(`Request to ${url} failed:`, error);
      throw error;
    }
  }
}
