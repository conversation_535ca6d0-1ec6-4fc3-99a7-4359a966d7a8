name: Deployment on qa environment
on:
  push:
    branches:
      - qa

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: qa
    permissions:
      deployments: write
    steps:
      - name: Deploy Eastward Qa Api
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{secrets.SSH_HOST}}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.DEPLOY_SSH_PRIVATE_KEY  }}
          script: |
            echo "✅ SSH connection established"
            export NVM_DIR="$HOME/.nvm" &&
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" &&
            nvm use 22.17 &&
            cd ~/eastward-api/ &&
            git reset --hard HEAD && git clean -fd &&
            git checkout qa &&
            git pull -r &&
            npm install
            npm run build
            pm2 restart eastwardqa-api